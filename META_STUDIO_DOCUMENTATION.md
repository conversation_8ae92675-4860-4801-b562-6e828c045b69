# Meta Studio 統合ドキュメント

## 📋 目次
1. [プロジェクト概要](#プロジェクト概要)
2. [アーキテクチャ](#アーキテクチャ)
3. [主要機能](#主要機能)
4. [API仕様](#api仕様)
5. [コンポーネント一覧](#コンポーネント一覧)
6. [ユーティリティ関数](#ユーティリティ関数)
7. [設定・環境](#設定環境)
8. [開発ガイド](#開発ガイド)

## 🎯 プロジェクト概要

### Meta Studio とは
次世代AI Vtuberプラットフォーム。VRMキャラクター、音声合成、ペルソナ分離、スラッシュトーク機能を統合した革新的なAIアシスタントシステム。

### 技術スタック
- **フロントエンド**: Next.js 15, React 18, TypeScript
- **UI**: DaisyUI, Tailwind CSS
- **3D**: Three.js, VRM
- **音声**: Web Audio API, Speech Recognition API
- **状態管理**: React Context, Zustand
- **データベース**: IndexedDB (VRM永続化)
- **パッケージマネージャー**: Bun

## 🏗️ アーキテクチャ

### ディレクトリ構造
```
meta-studio/
├── apps/web/                    # メインWebアプリケーション
│   ├── src/
│   │   ├── app/                 # Next.js App Router
│   │   ├── components/          # Reactコンポーネント
│   │   ├── contexts/            # React Context
│   │   ├── hooks/               # カスタムフック
│   │   ├── utils/               # ユーティリティ関数
│   │   └── types/               # TypeScript型定義
│   ├── public/                  # 静的ファイル
│   └── package.json
├── docs/                        # ドキュメント
├── requirements/                # 要件定義書
└── claude.md                    # Claude Code指示書
```

### 主要アーキテクチャパターン
1. **コンポーネント駆動開発**: 再利用可能なUIコンポーネント
2. **Context API**: グローバル状態管理
3. **カスタムフック**: ロジック分離と再利用
4. **プラグインシステム**: 拡張可能なモデルローダー

## 🚀 主要機能

### 1. VRMキャラクターシステム
- **VRMViewer**: 3D VRMモデル表示・制御
- **CharacterContext**: キャラクター状態管理
- **ModelPreviewSelector**: プレビュー形式モデル選択UI
- **VRM永続化**: IndexedDBによるモデル保存

### 2. 音声機能
- **TTS (Text-to-Speech)**: 音声合成システム
- **音声認識**: 連続音声入力対応
- **スラッシュトーク**: 音声コマンド分離機能

### 3. ペルソナ分離システム
- **PersonaManager**: チャット/ターミナル別人格
- **プリセットペルソナ**: 事前定義済み人格
- **動的切り替え**: コンテキスト自動判定

### 4. AI統合
- **Claude API**: メインAIエンジン
- **複数モデル対応**: Haiku, Sonnet, Opus
- **ストリーミング**: リアルタイム応答

## 📡 API仕様

### 主要APIエンドポイント

#### `/api/ai/chat` - AI チャット
```typescript
POST /api/ai/chat
Content-Type: application/json

{
  "message": string,
  "agentId": string,
  "model": "claude-3-haiku" | "claude-3-sonnet" | "claude-3-opus",
  "systemPrompt"?: string,
  "stream": boolean
}
```

#### `/api/claude-code` - Claude Code統合
```typescript
POST /api/claude-code
Content-Type: application/json

{
  "query": string,
  "persona"?: string,
  "context"?: string
}
```

#### `/api/files` - ファイル操作
```typescript
GET /api/files?path={path}&stats={boolean}
POST /api/files (ファイル作成)
PUT /api/files (ファイル更新)
DELETE /api/files (ファイル削除)
```

## 🧩 コンポーネント一覧

### レイアウト・ナビゲーション
- `MetaStudioLayout`: メインレイアウト
- `Sidebar`: サイドバーナビゲーション
- `TabManager`: タブ管理システム

### AI・キャラクター
- `ISSystem`: 統合AIシステム
- `VRMViewer`: VRMモデル表示
- `PersonaSelector`: ペルソナ選択UI
- `ModelPreviewSelector`: モデル選択UI

### 音声・入力
- `VoiceProcessingSystem`: 音声処理
- `NotificationSystem`: 通知システム
- `EnhancedFileExplorer`: 拡張ファイルエクスプローラー

### ダッシュボード
- `DashboardGrid`: ダッシュボードレイアウト
- `TimeBlockWidget`: タイムブロック管理
- `VisionWidget`: 目標管理
- `TaskWidget`: タスク管理

## 🔧 ユーティリティ関数

### 音声関連
- `text-to-speech.ts`: TTS管理
- `slash-talk.ts`: スラッシュトーク処理
- `useContinuousVoiceInput`: 音声入力フック

### データ管理
- `vrm-persistence.ts`: VRM永続化
- `persona-manager.ts`: ペルソナ管理
- `unified-model-loader.ts`: 統一モデルローダー

### UI・UX
- `useNotifications`: 通知管理
- `useTimeBlocks`: タイムブロック管理
- `useCharacter`: キャラクター管理

## ⚙️ 設定・環境

### 環境変数
```env
ANTHROPIC_API_KEY=your_claude_api_key
NEXT_PUBLIC_APP_URL=http://localhost:8080
NODE_OPTIONS=--max-old-space-size=8192 --no-deprecation
```

### 主要設定ファイル
- `next.config.js`: Next.js設定
- `tailwind.config.js`: Tailwind CSS + DaisyUI設定
- `tsconfig.json`: TypeScript設定
- `package.json`: 依存関係・スクリプト

### DaisyUIテーマ設定
```javascript
themes: [
  {
    metastudio: {
      primary: "#3b82f6",
      secondary: "#8b5cf6", 
      accent: "#06b6d4",
      neutral: "#374151",
      "base-100": "#ffffff",
      info: "#0ea5e9",
      success: "#10b981",
      warning: "#f59e0b",
      error: "#ef4444"
    }
  }
]
```

## 🛠️ 開発ガイド

### 開発サーバー起動
```bash
# 依存関係インストール
bun install

# 開発サーバー起動
cd apps/web
bun run dev

# アクセス: http://localhost:8080
```

### 主要開発コマンド
```bash
# 型チェック
bun run type-check

# リント
bun run lint

# ビルド
bun run build

# プロダクション起動
bun run start
```

### コーディング規約
1. **TypeScript必須**: 全てのファイルでTypeScript使用
2. **関数型プログラミング**: 純粋関数・不変性を重視
3. **コンポーネント分離**: 単一責任の原則
4. **カスタムフック**: ロジック再利用
5. **エラーハンドリング**: 適切な例外処理

### Git運用
- **ブランチ戦略**: main ブランチでの直接開発
- **コミット**: 機能単位での細かいコミット
- **メッセージ**: 日本語での明確な説明

## 📚 参考資料

### 外部ドキュメント
- [Next.js Documentation](https://nextjs.org/docs)
- [DaisyUI Components](https://daisyui.com/components/)
- [VRM Specification](https://vrm.dev/)
- [Claude API Reference](https://docs.anthropic.com/)

### 内部ドキュメント
- `claude.md`: Claude Code指示書
- `requirements/要件定義.md`: 詳細要件
- `task.md`: 開発タスク管理

---

**最終更新**: 2025-06-19
**バージョン**: v0.8.0
**メンテナー**: Meta Studio開発チーム
