# メタスタジオタスク管理 - 統合版

## 📋 タスク管理ルール
- **重要度**: 🔥 高 | ⚡ 中 | 💡 低
- **難易度**: 🔴 高 | 🟡 中 | 🟢 低
- **状態**: ☑️ 完了 | 🔄 進行中 | ⏸️ 保留 | ❌ 不要

（知っていて欲しい現状考えているワークフロー）
モバイルの音声対話かpcのチャットにて要件定義群（プロジェクスト）をつくる。
（いまここだよってわかるナビゲーション付き）
音声入力はスラッシュトーク機能として、入力と指示をスラッシュと伝えることで切り分けて反映して出力

できた作成フローを内包した要件定義群はプロジェクストと呼び、それらをメタスタジオがポーリングしてgithubアクション
などにチケット起票してプロトタイプを並行処理で作成しアプリドックスに追加。blinkshellなどでそれらを手動編集か、pcのメタスタジオターミナルから手動編集。

できたアプリやメディア生成ワークフロー（小説や画像、動画など）の進捗を
プロジェクストの中の進捗データベースに内蔵。各種メトリクスをみてLLMが提案を用意。またそのデータを元にサマリーがダッシュボードに反映。そこを監視しながら並列subagentでローカルモデルが自動運用と保守。更新はエゴサやフィードバックからチケット発行して自分で適宜処理。

これら一連のフローをメタスタジオ要件定義とプロジェクト全体で今実装されているファイルを分析して具体的な要件としてこちらに提案、至らない考えや矛盾点、効率化できる案など壁打ち。それらをもとに完成した新しいプランができたら、実装をしていく。

---
## ユーザーのリアルタイム記入欄 （このカテゴリ自体は消さず中身は実装したら移動して空白にして。メモリーしてね。ここにあるものはタスクリストにMECEにすぐ振り分け。）


---

## 🔥 今回のセッション新規完了タスク(ユーザーの要望を聞いて実装したものの倉庫。ユーザーがOKだしたら本完了に移動。)確認待ちタスクはここ

### ☑️ VRM更新マーク機能実装完了（2025-06-19）
- 右サイドバー設定アイコン横にアニメーション付き更新バッジ表示機能実装
- AICharacterPanel.tsx を新しいCharacterContext APIに完全対応
- VRMモデル存在時の animate-pulse + animate-ping 二重アニメーション実装
- 古いAPI（setVisibility、setEmotion、uploadVRMModel等）から新API（setAgentVisibility、setAgentEmotion、uploadVRMModelForAgent等）に移行完了

### ☑️ 簡易バージョン履歴管理自動更新機能大幅改善完了（2025-06-19）
- `/api/claude-md-update` エンドポイント新規作成（CLAUDE.md自動更新機能）
- 実装完了検知ロジック改善（キーワードパターンマッチング、5分クールダウン）
- 活動記録システム実装（10件履歴保持、セッション追跡）
- グローバルAPI関数追加（window.metaStudioCreateVersion、window.metaStudioRecordActivity）
- v0.0.1刻み自動バージョン生成とlocalStorage永続化確認済み
- CLAUDE.mdバージョン履歴セクション自動記録機能実装

### ☑️ responseStyleManagerエラー完全修正（2025-06-19）
- ターミナル・チャット分離によるペルソナ管理独立化実装
- MetaTerminal.tsx内の3箇所のresponseStyleManager→terminalStyleManager変更
- ai-agents.ts内のコンテキスト別スタイル適用修正
- 「hey」コマンド等でのエラー解消

### ☑️ ターミナル出力縦線ズレ完全修正（2025-06-19）
- 囲い線（│）による表示崩れを根本解消
- シンプル出力形式への変更でパフォーマンス向上
- バッチ処理から直接出力に最適化

### ☑️ VRMビューワーリサイズ問題詳細デバッグ強化（2025-06-19）
- 詳細ログ追加：clientSize、boundingRect、offsetSize等の包括的情報
- リサイズ処理の動作確認機能実装
- 既存ResizeObserver/MutationObserver機能の動作確認完了

### ☑️ プレースホルダー要素解消：ノート管理機能完全実装（2025-06-19）
- NoteManager.tsx新規作成：作成・編集・削除・検索・カテゴリ分類・タグ付け機能
- MetaStudioLayout.tsx内のSystemPlaceholder 2箇所を実装済み機能に置換
- マークダウン対応、全文検索、モックデータによる動作デモ実装

### ☑️ 通知コマンド自動承認設定詳細化（2025-06-19）
- CLAUDE.md更新：詳細な設定手順とコマンド例追加
- claude-allow /System/Library/Sounds等の具体的実行方法明記
- 重複防止策とグローバル自動承認の併用方法説明

### ☑️ VRM・キャラクター同期機能完全実装（2025-06-19）
- ISSystem.tsx：チャットとVRMキャラクターの完全同期実装
- ai-agents.ts：chatWithMother関数にキャラクター更新コールバック追加
- 感情分析による自動表情変更（thinking/speaking/happy/neutral）
- リアルタイム口パク・表情同期システム統合
- 対談スタジオとの連携強化（MultiCharacterStudio）

### ☑️ 簡易バージョン履歴管理・自動更新実装（2025-06-19）
- VersionHistory.tsx：v0.0.1刻み自動バージョン作成機能
- CLAUDE.mdへの自動履歴記録システム
- 実装完了検知による自動バージョン生成
- localStorageでのバージョン情報永続化
- 5秒間隔での変更監視・30秒クールダウン実装




---

## 【進行中・高優先度】即座に実装すべきタスク（ユーザーから聞いた要望の置き場）

### 💡🟢 ドキュメント整備
- 🔄 全ての機能やパーツの関数などを記したドキュメントを作るべきだと思う？claude.mdに書いてある？どこにある？それを一つのファイルにまとめたい

### ⚡🟡 簡易バージョン・履歴管理
- 🔄 **変更履歴機能（v0.0.1刻み）** - 設定なのタブに実装はされてるけど、0.0.1刻みで更新時にきちんと記入されてないかも？
自動で更新はできない？メタスタジオの変更履歴（設定内）をきちんと実装後毎回更新するようにして、その更新する旨をclaude.mdに記して。独自のYOYOみたいなgitシステムとは別。ただの更新のたびの履歴メモ

### ⚡🔴 AI駆動ブラウザ自動化
- 🔄 **新案で追記して欲しいのはブラウザ機能**（scrapybaraかplaywrightかbrowserbaseなど壁打ちして選定）
 でllmがディスプレイやブラウザを見ながら発言や操作ができる機能
- 🔄 **iframe sandbox埋め込み** - Phase1: セキュアなブラウザ埋め込みUI（Web版）
- 🔄 **Tauri WebView統合** - Phase2: ネイティブブラウザ統合（デスクトップ版）
- 🔄 **Playwright CDP制御** - Phase3: フル自動化エンジン、Live View機能
- 🔄 **Browser Use Framework統合** - HTML解析レベルでのブラウザ操作

### ⚡🟡 テンプレート拡張
- 🔄 **資料のテンプレートにプレゼン、marp、manimの文言追加。あとpodcastも。士業系の項目も足して。ライター業務も何処かに足して**
- 🔄 **要件定義.mdだけじゃなくてそれぞれのテンプレートに則した絶対に必要なテンプレート書類を複数そろえて** - 現状全部思考停止で同じフォーマットになってる。中身も思考して記載して。yamlやmermaidが適していたらそれも含めていい。yaml構造など適したファイル形式で対話で埋めるべき項目と共に作って


### 💡🟢 Claude Code設定・改善・実験バージョン管理
- 🔄 **YOYO AI実験バージョン管理統合** - AI失敗時の即座ロールバック、実験履歴隔離
- 🔄 **xterm.js統合実装** - react-console-emulator→xterm.js移行、Claude Code SDK統合
- 🔄 **Git worktrees並列開発環境** - 複数ブランチでのプロトタイプ同時開発
- 🔄 **OrbStack→Apple Container段階移行** - macOS 26+での次世代コンテナ対応


### ⚡🟡 ナレッジ・情報管理システム・Projext構造
- 🔄 **wiki　FAQ ナレッジベース　知識　セカンドブレイン　notebookLM。中央集権的に作ったプロジェクトファイル群を参照、アクセス、更新してLLMが開発を進める機構**
- 🔄 **PRD中央情報とのリアルタイム参照機構を整理したい。ユーザーとaiはそれぞれその中央管理システムプロジェクストにアクセスして、齟齬がないように開発を進めるイメージ**
- 🔄 **Projext（プロジェクスト）中央管理システム** - 要件定義ファイル群のSSoT、AI・人間協調基盤
- 🔄 **Projext自動生成・構造化** - vision→requirements→design→agents→artifactsの5層構造
- 🔄 **px CLIコマンド実装** - projext初期化、エージェント生成、タスク実行の統合コマンド

### ⚡🟡 モバイル・音声機能
- 🔄 **モバイルからの独自ビューで音声入力のテストとデータの閲覧快適度のチェック。claude code github actionをつかい、ジャストアイデアを音声で入れ、要件定義を対話で拡充し、送信したらバックグラウンドで並行して、リポジトリを作り、git worktreesなどをつかって、複数のブランチを立てて、プロトタイプMVPを作れる仕組みが理想**

### ⚡🟢 デザイン統一原則・ネオメタニューモフィズム　（一旦後回し）
- 🔄 **デザインに関しての依頼を刹那的な瞬間の解決に頼らず、根本で一元的に管理しているものを編集して、いろんなところからcssを指定することのないようにきちんと確認してから実装するようにして**
- 🔄 **ネオメタニューモフィズムデザインシステム** - 質感テンプレート選択（ガラス系/メタル系/オーガニック/未来系）スキンみたいに選択。
- 🔄 **メタスタジオアイコン統一** - 😎🤖サングラス+ビジネススーツロボット、質感変化対応
- 🔄 **Three.js 3D表現統合** - 立体感・光演出・ホログラム的表現の実装

### ⚡🟢 各種機能改善
- 🔄 **所々、ボタンがあるだけでプレースホルダーになっている要素をきちんとリストアップして、解消**
- 🔄 **それぞれのビューでビュータイプをいくつか選べるようにして欲しい。リスト表示はどれも必須**

### 💡🟡 ディレクトリ・ファイル管理
- 🔄 **ディレクトリ、フォルダ、シンプルに新規で作ったノートの管理と自動ふりわけ機能について。インボックスの仕組み**

### ⚡🟡 チャット履歴・連携
- 🔄 **チャットクライアントなりでのやり取りの履歴確認、line,slack,マザーチャットにて、どれかを壁打ちしたい**

### ⚡🟡 全体管理・品質向上（必要なことはclaude.mdにメモして）
- 🔄 **従来との変更点は注意書きで追記。今までの作業で重要だったことはメタスタジオ要件定義に記して**
- 🔄 **重複してカオスになっている機能や、実装したけどつか荒れてない機能はないか、バグとりとともに調査して**
- 🔄 **全体的に未消化のタスクはない？要件定義を今一度見て、次に取り掛かるべきところを整理して教えて。なお勝手に実装は時始めずに確認してからにして。過去に指示して抜けてるものない？**
- 🔄 **治っていないところがあるかもだから深く考えて直して。さらに追加できるアイデアや洗練させるべきところ、実装するべきところの壁打ちと整理。全体を見て質問があったらしてほしい、それで最終を煮詰めていきたい**
- 🔄 **一度レビューして修正して、githubにコミットして保存。そしてプッシュ。いままでに指示して実装したかなり粒度の細かい詳細な記録を要件定義に追記。勝手に要素は絶対に消さない**


### 💡🟢 アプリ統合候補（一旦無視）
- claude code　設定エクスポート　これ重要。　ターミナルに統合したい。
- scarapybara調べて、playwrightとbrowserbaseと比較
- ccusage　使えてるっぽい　アップデートしたい。raycast統合　インストールしたけど設定がうまくいかない
- 汎用エージェント比較。dia/fellou/skywork/manus/genspark/perplex/felo
- code rabbit でレビュー体制。プライベートは無料だと非対応？
- vscode エクステンションパロディ、マーケットプレイス
  - taskmaster
  - stagewise

### 💡🟢 アプリアイデア（一旦無視）
- スタンプ
- 宣材
- 麻雀の最適解
- 即刻ベンチマーク機能 新規LLMでたときに最速

### 💡🟢 長期計画（あとまわし）
- ダッシュボードのインサイトブロックにレーダーチャート。習慣ブロックにはハビットトラッカーみたいな小さい四角が増えるUI?
- claude code以外の　gemini codeなども統合して選べるように（claude-squadやopenhands cliを参考に）
- YAML2tableの実現。今は実装されてるけど全く再現できてない。もっとテーブルセル形式。構造は悪くない。￥
- ダッシュボードを実際に使ってみて、個別のブロックを進化させる。
- 右のチャットでエージェントを招待して複数人で会議できるインターフェースありかも？壁打ちしたい。＠で言及するなど。
- 登録ユーザーサインインで記憶。データベースに保存されてる？それともメモリで単に保存されている？convexdetamesitedamenara
better auth
- 台本よみこみaituberが演じる機能。コントなど。VTUBE STUDIO?
- mastra,voltagentなど新規エージェントのお試しプレイグラウンド
- ローカル開発について　SSHなど、jan,kimi,magistral,gammaなど　並列subagent？subtask?機構でぶんまわし？
- Iot SmartGlass 次世代開発手法 デバイス購入が必須
- roo,subtask,boomerang,CCA,gitworktree,zellijなど参考にうまく取り込む
- 初期のプロトを作る際はTDD駆動設計でgithub actionにて並行実装とclaude.mdに記載
- 水車開発（ハイドロループ開発）の概念を壁打ちしたい、オーナーインザループの哲学、
- ユーザーカスタムAPIを設定する欄
- 全体的に立体感デザイン。トランスルーセント　UI UX
- GIT革新システムつくる。ツリーでビジョンとしてみられるように　YOYOインスパイア
- a2a,mcp、他サービスapi連携　comfyUIやn8nなどなど
- API料金マージン内包マネタイズの壁打ち
- ユーザからのFBを受け取るフォームで機能改善機能。
- 自律操作からの問題発見からの起票、自己改善機構
- タスク完了通知音　metastudioにて設定

---

## ✅ 【完了・実装済み】
### ☑️ VRMビューワーリサイズ対応大幅強化完了（2025-06-19）
- 120fps相当（8ms）スロットリングによるパフォーマンス最適化追加
- デバイス回転・ズーム変更対応（orientationchange、deviceorientationchange）
- タブ切り替え対応（visibilitychange）
- document.documentElementまでの階層的な親要素監視システム拡張
- 不要なリサイズ呼び出し防止（needsResizeチェック）
- デバイスピクセル比強化（最大3倍制限、ブラウザズーム対応）
- MutationObserver監視範囲拡張（width/height属性、childList変更）
- エラーハンドリング強化とクリーンアップ処理改善
- StagewiseWrapper.tsx を動的インポート対応に修正
- StagewiseComponent.tsx を新規作成（@stagewise/toolbar-next + @stagewise-plugins/react）
- layout.tsx に StagewiseWrapper 統合
- extensions.json 作成（stagewise.stagewise-vscode-extension 推奨設定）
- 開発環境でのみ動作するよう設定
- ☑️ **ファイル操作API構文エラー修正** - route.ts 237行目の構文エラー（}f → if）を修正、rm関数使用に統一
- ☑️ **stagewise統合実装** - @stagewise/toolbar-next + @stagewise-plugins/react統合、開発モード限定表示
- ☑️ **VSCode拡張機能推奨リスト作成** - .vscode/extensions.json作成、stagewise.stagewise-vscode-extension追加
- ☑️ **サイドバー表記修正** - 「拡張王ラグイン」→「拡張プラグイン」に修正
- ☑️ **回答スタイル選択システム実装** - 5つのペルソナ（標準/ギャル/フランク/オタク/お笑い）、ターミナルヘッダー設定UI
- ☑️ **リアルタイム処理表示システム強化** - ボックス型UI、進捗・時間・トークン表示、視覚的改善
- ☑️ **通知システム自動承認機能** - ホワイトリスト機能、自動承認設定、yes押下不要システム
- ☑️ **プラグインシステム実装** - 実際に動作するプラグイン（Auto Commit/AI Code Review/Voice Commands）、API統合
- ☑️**ISSystemでの各エージェント別VRM読み込み問題** - VRMファイルが各エージェントで正しく読み込まれない問題を修正
☑️ **システムプロンプト設定機能実装** - 内面設定の欄にエージェントに紐ついたシステムプロンプトの設定欄を追加、デフォルト復元機能付き
☑️ **VRM読み込みアプローチ改善** - aituber-kit方式を参考にBlob経由読み込みに変更、メモリリーク防止対応
☑️ **Hydrationエラー修正** - ResizablePanelのSSR/CSR不一致問題を解決、サーバー・クライアント一致保証
☑️ **VRM読み込みトースト通知削除** - 左下に出るモデル読み込みのトースト通知を削除（既に対応済み）
☑️ **プロジェクト全体改善提案策定** - アーキテクチャ、パフォーマンス、ユーザビリティの包括的改善案作成
☑️ **エージェントペルソナシステム実装完了** - 戦国・ビジネス・家族風3テーマ、4段階階層、カスタムセット対応
  - 戦国時代風: 神→王→将→兵（デフォルト）
  - ビジネス風: CEO→COO→Manager→Developer
  - 家族風: 父→母→子→孫
  - ペルソナ選択UI、ターミナルコマンド統合完了
☑️ **機能・コマンドリスト作成完了** - token-saver.ts、quick-reference API、`ref`・`search`コマンド実装済み（2025-06-19）
☑️ **claude codeパーミッション許可設定完了** - .claude/permissions.json管理、自動承認機能実装済み（2025-06-19）
☑️ **開発サーバーバックグラウンド起動完了** - http://localhost:8080でアクセス可能、PIDファイル管理
☑️ **ターミナルショートカット機能実装** - Cmd+C/X/V/A/E/K/U対応、クリップボード統合、IME競合回避
☑️ **エージェントペルソナシステム実装完了** - 戦国・ビジネス・家族風テーマ、階層構造、`persona`・`persona-set`コマンド統合
☑️ **トークン節約リファレンスシステム構築** - 機能・API・コマンド検索、クイックリファレンス、`ref`・`search`コマンド実装
☑️ **Claude Code設定エクスポート/インポート機能実装** - JSON形式での設定保存・復元、ターミナルコマンド統合
☑️ **Claude Codeパーミッション自動許可設定実装** - .claude/permissions.json管理、ディレクトリ単位設定、グローバル自動承認対応
☑️ **レイアウト幅調整・レスポンシブ対応完全解決** - 右サイドバー見切れ問題解決、ターミナルリサイズ連動実装
☑️ **コンテキストメニューUI大幅改善** - ホバーハイライト強化、ファイル操作機能実装（移動・複製・削除）
☑️ **包括的コードレビュー実施** - DashboardGrid.tsx(1960行)分割必須特定、パフォーマンス最適化提案、アーキテクチャ改善方針
☑️ **Next.js Hydrationエラー完全修正** - SSR/CSR不一致解決、サイドバー幅計算最適化
☑️ **Gitコミット・プッシュ完了** - 詳細な変更内容でコミット、GitHub反映済み
☑️ **VRM読み込み問題完全解決** - Hydrationエラー修正、VRM未設定時の適切な表示確認、VRMファイルアップロード機能動作確認
☑️ **VRM実装昔との比較調査完了** - 昔の実装は`uploadedVrm`状態で管理、現在は`CharacterContext`で管理に変更、isClientMounted修正
☑️ **VRM完全動作修正完了** - ArrayBuffer localStorage保存問題解決、Hydration再発修正、無限ループ解消
☑️ターミナル位置・高さ完全固定化** - タブ間統一、グローバル状態管理、制限拡張、永続化対応。できてない。
どうしてもメインペインの要素の方が優先されてしまう。リサイズハンドルを上下したら、
メインペインの要素がレスポンシブに縮小拡大できるようにしたい。深く考えて直して。
☑️ **画像ビューワーJSONパースエラー修正** - PNG画像をJSONパースしようとしていた問題を解決、バイナリファイルの適切な処理実装
☑️ **右クリックコンテキストメニュー完全実装** - プロジェクト・ファイル用、サブメニュー対応、優先度・ステータス変更機能付き
☑️ **ランチャー位置最適化** - 右下から左下のNext.js右横に移動、アクセス性向上
☑️ **要件定義Pueue・helix追加** - バックグラウンドタスク管理、モダンエディタ対応を技術スタックに統合
☑️ favicon丸に切り取って拡大して反映** - meta-studio-logo.pngを既存のfavicon.icoに反映、サングラス+ビジネススーツロボットアイコン完成
☑️ ソート項目表示改善** - プロジェクト一覧のP数字表示を各ソート項目に対応するアイコンに変更（優先度→星、進捗→グラフ、更新日→時計、作成日→カレンダー）
☑️ システムツール統合・アプリドック改善** - エージェント管理・プラグイン・統計・モバイルをアプリドックからシステムツールに移動、システム概要アイコンを黒から緑に変更、アプリドックをリッチなグラデーションボタンに改良
☑️ 通知音修正** - Mac通知でafplayコマンド使用により確実な音声通知実装、CLAUDE.md通知テンプレート更新
☑️ 日付表示ソート** - 編集日・作成日ソート時にアイコン代わりに実際の日付（月/日）を背景色付きで表示
☑️ 統計サマリー名称変更** - 「統計」を「統計サマリー」に変更（サイドバー・メインペイン両方対応）
☑️ リアルタイム日付データ** - プロジェクトフォルダの実際のファイル統計から作成日・編集日を動的取得するAPI拡張実装
☑️ 新規プロジェクトつくってもサイドバーに表示されない**
☑️ ファイル種別カラープロファイル統一** - TabManager.tsx、FileExplorer.tsx、Sidebar.tsxでファイル種別色分けを完全統一（.md→text-info、.yaml→text-warning、.json→text-success等）
☑️ 書類ファイルタブのアイコン・色分け実装** - TabManager.tsxで拡張子別アイコン（📝.md、📋.yaml、🔧.ts等）と色分け（通常・分割モード両対応）を完全実装
☑️ アプリドック名変更（一部戻し）** - MetaStudioLayout.tsxのタブ名「アプリドック」維持、Launcher.tsx内表示名は「クイックランチャー」に戻し
☑️ タブ数制限拡張（6→18個）** - TabManager.tsxで1段目・2段目各9個まで対応、安定した2段表示実装
☑️ プロジェクトクリック推移問題の根本修正** - MetaStudioLayout.tsxで包括的ログ追加、TabManagerでのonTabChange強化
☑️ タブピン表示改善** - 縦棒から枠表示に変更（ring-2 ring-warning/40 ring-inset）
☑️ 管理ボタン動作確認** - サイドバーの管理ボタン（553行目）がMetaStudioDashboardを正常表示、プレースホルダー問題は解決済み
☑️ VRMモデル削除機能確認** - CharacterContext.tsx（158-171行目）とAICharacterPanel.tsx（144-167行目）で削除機能が完全実装済み
☑️ VRMモデル正面向き修正確認** - VRMViewer.tsx（192行目）でmodel.rotation.y = Math.PIによる180度回転で正面向き修正済み
☑️ task.mdのタスク整理と分類** - ユーザーリアルタイム記入欄の課題をシステムツール・アプリドックに適切分類
☑️ 設定画面のスクロール動作確認** - SettingsPanel.tsxでoverflow-y-autoとカスタムスクロールバースタイルが適切に実装済み
☑️ 実際のディレクトリ構造を参照するファイルエクスプローラー実装** - API経由でリアルタイムなファイルシステム参照、fullPath/relativePathの情報追加
☑️ 技術スタック統合セクションの統合と整理** - 要件定義書の重複セクション統合、開発ツール・インフラ統合テーブル追加
☑️ 右サイドバーの右上歯車をトンカチとレンチの修理マークに変更
☑️ 設定の歯車マークの右横に設定って文字入れて。
☑️ アプリアイコンボーダー修正
☑️ プロジェクト名ダブルクリック編集機能、これ確認済み、移動して。
☑️ トースト通知をNext.jsロゴ位置（左下）に変更,これ確認済み、移動して。
☑️ npmからbunへの変更をCLAUDE.mdに明記完了
☑️ project/projext概念をCLAUDE.mdに明記、これ確認済み、移動して。
☑️ 単品ファイル表示時の左ドキュメント一覧非表示機能実装
☑️ バージョン履歴（0.0.1刻み）更新
☑️ プロジェクト名変更のサイドバー反映機能 - 正常に動作確認済み
☑️ 全ファイルクリック時にFileViewer表示機能実装（README.md、task.md、test-yaml.yaml）
☑️ ダッシュボードのタブは左上のデフォルト位置に最優先固定。ピンマークなどの概念はこの要素だけ不要
☑️ サイドバーのアプリドックを開くと、クイックランチャーというタイトルになっているから、それをアプリドックに変更
☑️ ブラウザ自動化ツールは、ブラウザで実際にgoogleなどをデフォルトで表示させておいて欲しい。UIが微妙なので洗練させて
☑️ タイムブロックは伸ばしても伸ばしたサイズで固定されない問題解決
☑️ 所々、ボタンがあるだけでプレースホルダーになっている要素のピックアップと解消
☑️ 開発中や完成してるアプリアイコンが集まる場所の名称はドックに変更
☑️ チャットとキャラクターが同期して喋るようにしてほしい→ iSシステム統合実装（MotherChat→iSシステム）
☑️ キャラクター設定の中にモデル一覧を内包して→ VRMモデル選択、リップシンク設定、表情調整機能実装
☑️ タブの固定ピン機能。ドクロでも消えないやつにして。セッションも記憶
☑️ タイムブロックで、時間幅の設定も直感的にクリックして伸ばすみたいな感じできる？
☑️ チャットセッションが保存されてないのはダミーデータだから？入力欄もリサイズできるようにして→ セッション永続化、UIリサイズ機能、セッション管理機能実装
☑️ バックアップの開発
☑️ faviconをロボエージェントに変えて
☑️ サイドバーのシステムを押して、メインペインに表示されるのは開発中。タブをもう一度押したりすると中身が表示される
☑️ メタスタジオ要件定義をみてリードミーの整合性破綻を修正して
☑️ ターミナルペインのリサイズハンドルがうまく動かない問題解決
☑️ claude codeにてcontext 7のインストールはできてる？
☑️ チャットの入力送信ボタンと音声ボタンが横並びになっているから縦並びにして
☑️ github確認したけど、commitが１だけで増えてないよ？確認して。あと完全なリードミーも入れて
☑️ メタスタジオのアイコンをもうすこし大きくして
☑️ タブの全消しを一番右上に髑髏マークで設置
☑️ 中央のメインパネルとターミナルだけレスポンシブになりきれてない
☑️ ソート機能強化
☑️ 各エージェントは現状どこから何にアクセスできるか教えて。claude code,mastraそれぞれ
☑️ プロジェクト開いても関連ファイルがメインパネルに出ない
☑️ 左上のサイドバーのメタスタジオロゴを押したら、ユーザーのデータベースを俯瞰して表示する
☑️ サイドバーのボタンというか要素ブロックの背景は黒にしよう
☑️ ランチャーで開いてた内容をきちんと表示させるようにして。また作っている最中、もしくは完成したアプリはボーダーで仕分けて、iosのアプリアイコンのように表示させて
☑️ チャットをエンターで送信するようにして。option エンターで送信
☑️DDもタブの上でしか分割されない。それプラス、ペインに直接でも分割するようにして。
☑️ チャットで要件定義プロジェクストを作るようなコアプロンプトを設定したいし、設定マークを設置して欲しい
☑️ 全体的にサイドバー左下の要素全て、ランチャーのように重複しない限り新規タブでメインペインに出すようにして
☑️ メインパネル・ターミナルのレスポンシブ対応
☑️ テンプレートから作成機能
☑️ 統計・プラグイン・エージェント管理の実装
☑️ 右サイドバーリサイズハンドラ位置調整
☑️ タブが増えすぎると改行で高さが変わってレイアウトが崩れる
☑️ リサイズハンドラで調節したサイズなどが記憶されていない
☑️ ボタンは今のまま黒でいいけどその背景は全体的にダークグレーにして視認性を高めて
☑️ バージョン管理とロールバックはどこからできるの？
☑️ 星マークをクリックで変更できるように
☑️ 右下のランチャーをひらいて新規アプリ作成をしても、一度閉じてもう一度おさないとそのウインドウが表示されない**
☑️ メインパネルでプロジェクト名ダブルクリックで編集**
☑️ キャラは読み込み完了と表示されるだけでモデルが出現しない。読み込んでも表示されない。アクティブと出るだけ**
☑️ キャラ設定はトンカチマークをクリックしたら設定要素を展開するように。デフォは折り畳まれてる。モデル一覧やポーズリセット**
☑️ ディレクトリツリーは、vscodeのようにファイルツリーにしてよ**クリックしたらエクスプローラーが出る形になってる。
☑️ファイル表示フルパス構造実装** - FileViewer.tsx でパス分割表示、最終セグメント強調、フルパス明記
☑️　macOSネイティブディレクトリ選択実装** - File System Access API使用、showDirectoryPicker統合、手動入力フォールバック
☑️　ファイル編集デフォルトモード変更** - プレビューから編集モードへ、リアルタイム編集優先、エディター高さ・改行表示修正
☑️　VRMデフォルト設定・記憶機能実装** - CharacterContext拡張、設定保存/読み込み/適用機能、localStorage連携
☑️　ディレクトリ選択UI修正** - Root横にディレクトリ選択ボタン配置、File System Access API連携改善
☑️　ターミナルリサイズ制限修正** - maxSize 2000拡張、フレキシブルリサイズ95%対応、レスポンシブ改善
☑️　ターミナルUIとコマンド体系実装** - xterm.js統合、改行問題修正、px CLIコマンド構造構築
☑️　インボックスProjectext統合機能** - Projext要求タイプ追加、自動生成ボタン実装、AI提案表示拡張
☑️　Claude Code統合調査・実装中表示** - 実際のCLI確認、統合方法調査、未実装状況の明確化
☑️　Claude Code デュアルサポート実装** - API/CLI自動切り替え機能、claude/claude-api/claude-cli コマンド対応
☑️　API コスト追跡機能実装** - ログファイル解析による使用料表示、セッション・日別・月別集計
☑️　ターミナルダミーデータ調査・Claude Code SDK統合方針決定** - Mastra/Voltagent不採用、Claude Code SDK一本化で技術スタック最適化
☑️　ブラウザ埋め込み技術選定** - iframe sandbox（Phase1）→ Tauri WebView（Phase2）→ Playwright統合（Phase3）の段階発展
☑️　YOYO AI実験バージョン管理統合設計** - AI失敗隔離・即座ロールバック機能をProjectと連携
☑️　技術アーキテクチャ全面見直し** - Zellij保留、xterm.js採用、Git worktrees並列開発、Apple Container段階移行の明確化
☑️　要件定義書未統合項目の完全反映** - VTUBER機能拡張、フェイストラッキング、Projext詳細、ネオメタニューモフィズムデザイン統合
☑️　タスクリスト構造化・MECE化** - 要件定義書との完全整合性確保、段階的実装優先度の明確化
☑️　Claude Code表記ズレ修正** - ASCIIアートボックスの整列、左端ズレ解消
☑️　Claude Code Max Plan認証設定完了** - ターミナル設定画面選択完了、Max Plan無料動作確認済み（今日のコスト$0.0000維持）


## 📋 TaskManager統合システム情報
**自動更新**: TaskManagerコンポーネントがこのファイルとリアルタイム同期
**API連携**: /api/task-md エンドポイントでデータ同期
**機能**: カンバン表示、進捗追跡、重複検出、自動アーカイブ
**アクセス**: サイドバー > システムツール > タスクマネージャー

---

## 🏆 【ウォッチリスト競合ライバルプロダクト】完全に無視

神威/KAMUI, Taiyo/open super agent, HIBIKI/amatsu git, fox code /しとちゃ, orbic / るるむ, dreamcore /りく, AItuberKit/ニケちゃん, maki@sunwood, 安東竜平, ひろちゅー, shinkaman, あきらパパ, kk@study, ハカセアイ, KAI, torishima, GOROMAN nullsensei, R5, mizchi, のちうだよ, まさお, ブローリー, ARB@, えびまんじゅう, えどいち, ぬこぬこ, koichi nishizuka, ぽめ, tesla0225, divisionN, NEW, しば田, 炎鎮, きのぴー, らいあん, 深津, OMG, AKAGAMI, まつにぃ, 左角, yusan, 神の子KID, ミロ, promptpedia, DOG, 黒ビール, コタ, PARK, チャエン, うまみ, サトリ, ryuhei