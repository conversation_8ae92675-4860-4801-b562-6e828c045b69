# AI駆動ブラウザ自動化システム設計書

## 📋 概要

Meta StudioにAI駆動のブラウザ自動化機能を統合し、LLMがディスプレイやブラウザを見ながら発言や操作ができるシステムを構築する。

## 🎯 目標

1. **視覚的理解**: LLMがブラウザ画面を認識・理解
2. **自動操作**: AI指示によるブラウザ操作の実行
3. **セキュリティ**: サンドボックス環境での安全な実行
4. **統合性**: Meta Studioとのシームレスな連携

## 🏗️ アーキテクチャ設計

### Phase 1: セキュアなブラウザ埋め込みUI（Web版）
```
┌─────────────────────────────────────┐
│ Meta Studio Web Interface          │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ Iframe Sandbox Browser          │ │
│ │ - CSP制限                       │ │
│ │ - 限定的DOM操作                 │ │
│ │ │ ┌─────────────────────────┐   │ │
│ │ │ │ Target Website          │   │ │
│ │ │ │ (sandboxed)             │   │ │
│ │ │ └─────────────────────────┘   │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ AI Vision & Control Panel      │ │
│ │ - スクリーンショット解析        │ │
│ │ - 操作指示入力                  │ │
│ │ - 実行ログ表示                  │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### Phase 2: ネイティブブラウザ統合（デスクトップ版）
```
┌─────────────────────────────────────┐
│ Tauri Desktop Application          │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ Native WebView                  │ │
│ │ - フルブラウザ機能              │ │
│ │ - ネイティブAPI連携             │ │
│ │ └─────────────────────────────┘ │ │
│ ┌─────────────────────────────────┐ │
│ │ Rust Backend                    │ │
│ │ - WebDriver制御                 │ │
│ │ - システム統合                  │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### Phase 3: フル自動化エンジン
```
┌─────────────────────────────────────┐
│ Playwright CDP Control Engine      │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ Browser Automation              │ │
│ │ - Headless/Headed Browser       │ │
│ │ - Multi-tab Management          │ │
│ │ - Session Persistence           │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ AI Vision Pipeline              │ │
│ │ - Screenshot Analysis           │ │
│ │ - Element Detection             │ │
│ │ - Action Planning               │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ Live View & Control             │ │
│ │ - Real-time Streaming           │ │
│ │ - Manual Override               │ │
│ │ - Recording & Playback          │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 🔧 技術選定

### ブラウザ自動化ライブラリ比較

#### 1. Playwright ⭐⭐⭐⭐⭐
**推奨度**: 最高
```typescript
// 利点
- 高性能・安定性
- マルチブラウザ対応
- 豊富なAPI
- TypeScript完全対応
- スクリーンショット・録画機能

// 欠点
- 学習コストやや高
- リソース消費量多
```

#### 2. Browser-Use Framework ⭐⭐⭐⭐
**推奨度**: 高
```python
# 利点
- AI特化設計
- HTML解析レベル操作
- 自然言語指示対応
- 豊富なプリセット

# 欠点
- Python依存
- 新しいライブラリ
```

#### 3. Scrapy + Selenium ⭐⭐⭐
**推奨度**: 中
```python
# 利点
- 成熟したエコシステム
- 豊富なドキュメント
- 大規模スクレイピング対応

# 欠点
- 設定複雑
- パフォーマンス課題
```

#### 4. BrowserBase ⭐⭐⭐⭐
**推奨度**: 高（クラウド版）
```typescript
// 利点
- クラウドベース
- スケーラブル
- メンテナンス不要
- 高速起動

// 欠点
- 外部依存
- コスト発生
```

### 最終選定: **Playwright + Browser-Use Framework**

## 📦 実装計画

### Phase 1: Iframe Sandbox実装（2週間）

#### 1.1 基本コンポーネント作成
```typescript
// apps/web/src/components/BrowserSandbox.tsx
interface BrowserSandboxProps {
  url?: string;
  onScreenshot?: (imageData: string) => void;
  onAction?: (action: BrowserAction) => void;
  restrictions?: SecurityRestrictions;
}
```

#### 1.2 セキュリティ設定
```typescript
// CSP設定
const sandboxPolicy = {
  'sandbox': 'allow-scripts allow-same-origin allow-forms',
  'allow': 'camera; microphone; geolocation',
  'csp': "default-src 'self'; script-src 'unsafe-inline'"
};
```

#### 1.3 AI Vision統合
```typescript
// Claude Vision APIとの連携
const analyzeScreenshot = async (imageData: string) => {
  const response = await fetch('/api/ai/vision', {
    method: 'POST',
    body: JSON.stringify({
      image: imageData,
      prompt: 'このWebページの内容を分析し、操作可能な要素を特定してください'
    })
  });
};
```

### Phase 2: Tauri WebView統合（3週間）

#### 2.1 Tauri設定
```toml
# src-tauri/Cargo.toml
[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
tauri-plugin-window = "1.0"
webview2 = "0.1"
```

#### 2.2 ネイティブブラウザ制御
```rust
// src-tauri/src/browser.rs
use tauri::command;
use webview2::prelude::*;

#[command]
async fn create_browser_window(url: String) -> Result<String, String> {
    // WebView2でブラウザウィンドウ作成
}

#[command]
async fn take_screenshot() -> Result<Vec<u8>, String> {
    // スクリーンショット取得
}
```

### Phase 3: Playwright統合（4週間）

#### 3.1 Playwright設定
```typescript
// apps/web/src/utils/playwright-controller.ts
import { chromium, Browser, Page } from 'playwright';

export class PlaywrightController {
  private browser: Browser | null = null;
  private page: Page | null = null;

  async initialize() {
    this.browser = await chromium.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  }

  async navigateTo(url: string) {
    if (!this.page) {
      this.page = await this.browser!.newPage();
    }
    await this.page.goto(url);
  }

  async takeScreenshot(): Promise<Buffer> {
    return await this.page!.screenshot();
  }

  async performAction(action: BrowserAction) {
    switch (action.type) {
      case 'click':
        await this.page!.click(action.selector);
        break;
      case 'type':
        await this.page!.fill(action.selector, action.text);
        break;
      case 'scroll':
        await this.page!.evaluate(() => window.scrollBy(0, 500));
        break;
    }
  }
}
```

#### 3.2 Browser-Use Framework統合
```python
# scripts/browser_use_bridge.py
from browser_use import Agent
import asyncio
import json

class BrowserUseAgent:
    def __init__(self):
        self.agent = Agent(
            task="ブラウザ操作を実行",
            llm=claude_llm,
            use_vision=True
        )
    
    async def execute_task(self, instruction: str):
        result = await self.agent.run(instruction)
        return {
            'success': True,
            'actions': result.actions,
            'screenshot': result.screenshot
        }
```

## 🔒 セキュリティ考慮事項

### 1. サンドボックス制限
```typescript
const securityConfig = {
  // iframe制限
  sandbox: [
    'allow-scripts',
    'allow-same-origin',
    'allow-forms'
  ],
  
  // CSP設定
  contentSecurityPolicy: {
    'default-src': "'self'",
    'script-src': "'self' 'unsafe-inline'",
    'connect-src': "'self' https:",
    'img-src': "'self' data: https:",
    'style-src': "'self' 'unsafe-inline'"
  },
  
  // 許可ドメイン
  allowedDomains: [
    'example.com',
    'trusted-site.com'
  ]
};
```

### 2. 操作制限
```typescript
const operationLimits = {
  // 時間制限
  maxSessionTime: 30 * 60 * 1000, // 30分
  
  // 操作制限
  maxActionsPerMinute: 60,
  
  // リソース制限
  maxMemoryUsage: 512 * 1024 * 1024, // 512MB
  
  // 禁止操作
  forbiddenActions: [
    'file-download',
    'payment-form',
    'login-form'
  ]
};
```

## 📊 UI/UX設計

### ブラウザ自動化パネル
```typescript
interface BrowserAutomationPanelProps {
  mode: 'sandbox' | 'native' | 'automation';
  onModeChange: (mode: string) => void;
  onInstructionSubmit: (instruction: string) => void;
  isExecuting: boolean;
  executionLog: ExecutionStep[];
}
```

### 実行ログ表示
```typescript
interface ExecutionStep {
  id: string;
  timestamp: Date;
  type: 'navigation' | 'click' | 'type' | 'scroll' | 'screenshot';
  description: string;
  success: boolean;
  screenshot?: string;
  error?: string;
}
```

## 🚀 統合ポイント

### 1. Meta Studio連携
- エージェントペルソナとの連携
- 音声指示からブラウザ操作への変換
- VRMキャラクターによる操作説明

### 2. AI機能統合
- Claude Vision APIでの画面解析
- 自然言語指示の操作変換
- 実行結果の音声フィードバック

### 3. 通知システム連携
- 操作開始・完了通知
- エラー発生時のアラート
- 進捗状況の表示

## 📈 開発スケジュール

### Week 1-2: Phase 1 実装
- [ ] Iframe Sandbox基本実装
- [ ] セキュリティ設定
- [ ] 基本UI作成

### Week 3-4: Phase 1 完成
- [ ] AI Vision統合
- [ ] 操作ログ機能
- [ ] テスト・デバッグ

### Week 5-7: Phase 2 実装
- [ ] Tauri環境構築
- [ ] ネイティブWebView統合
- [ ] デスクトップ版UI

### Week 8-11: Phase 3 実装
- [ ] Playwright統合
- [ ] Browser-Use Framework連携
- [ ] 高度な自動化機能

### Week 12: 統合・テスト
- [ ] 全機能統合テスト
- [ ] パフォーマンス最適化
- [ ] ドキュメント整備

## 📚 参考資料

- [Playwright Documentation](https://playwright.dev/)
- [Browser-Use Framework](https://github.com/browser-use/browser-use)
- [Tauri WebView Guide](https://tauri.app/v1/guides/features/webview)
- [Web Security Best Practices](https://owasp.org/www-project-web-security-testing-guide/)

---

**最終更新**: 2025-06-19
**ステータス**: 設計完了・実装準備中
