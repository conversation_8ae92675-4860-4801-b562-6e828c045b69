'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

export interface VoiceInputOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
  onResult?: (text: string) => void;
}

export const useContinuousVoiceInput = (options: VoiceInputOptions = {}) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const recognitionRef = useRef<any>(null);
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // デフォルトオプション
  const {
    language = 'ja-JP',
    continuous = true,
    interimResults = true,
    maxAlternatives = 1,
    onResult
  } = options;

  // ブラウザサポート確認
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = 
        window.SpeechRecognition || 
        (window as any).webkitSpeechRecognition;
      
      setIsSupported(!!SpeechRecognition);
      
      if (SpeechRecognition) {
        recognitionRef.current = new SpeechRecognition();
        setupRecognition();
      }
    }
  }, []);

  const setupRecognition = useCallback(() => {
    if (!recognitionRef.current) return;

    const recognition = recognitionRef.current;
    
    recognition.continuous = continuous;
    recognition.interimResults = interimResults;
    recognition.lang = language;
    recognition.maxAlternatives = maxAlternatives;

    recognition.onstart = () => {
      setError(null);
      console.log('音声認識開始');
    };

    recognition.onresult = (event: any) => {
      let finalTranscript = '';
      let interim = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcriptText = result[0].transcript;

        if (result.isFinal) {
          finalTranscript += transcriptText;
          
          // 「どうぞ」キーワードで停止
          if (transcriptText.toLowerCase().includes('どうぞ')) {
            finalTranscript = finalTranscript.replace(/どうぞ/gi, '').trim();
            stopListening();
          }
        } else {
          interim += transcriptText;
        }
      }

      if (finalTranscript) {
        setTranscript(prev => {
          const newTranscript = prev + finalTranscript;
          // onResultコールバックを呼び出し
          if (onResult) {
            onResult(newTranscript);
          }
          return newTranscript;
        });
      }
      setInterimTranscript(interim);
    };

    recognition.onerror = (event: any) => {
      console.warn('音声認識エラー:', event.error);

      // エラーの種類に応じて処理を分岐
      switch (event.error) {
        case 'network':
          console.log('🌐 ネットワークエラー - 自動再開を試行');
          setError('ネットワーク接続を確認中...');
          // 少し待ってから再開
          setTimeout(() => {
            if (isListening) {
              restartListening();
            }
          }, 2000);
          break;

        case 'aborted':
          console.log('🛑 音声認識が中断されました');
          // 意図的な中断の場合は何もしない
          break;

        case 'audio-capture':
          setError('マイクへのアクセスが拒否されました。ブラウザの設定を確認してください。');
          setIsListening(false);
          break;

        case 'not-allowed':
          setError('マイクの使用許可が必要です。ブラウザの設定を確認してください。');
          setIsListening(false);
          break;

        case 'service-not-allowed':
          setError('音声認識サービスが利用できません。');
          setIsListening(false);
          break;

        case 'bad-grammar':
          console.warn('⚠️ 音声認識の文法エラー');
          setError('音声認識の設定エラーが発生しました。');
          break;

        case 'language-not-supported':
          setError('選択された言語はサポートされていません。');
          setIsListening(false);
          break;

        default:
          console.error('❌ 未知の音声認識エラー:', event.error);
          setError(`音声認識エラー: ${event.error}`);

          // 一般的なエラーの場合は再開を試行
          if (isListening) {
            setTimeout(() => {
              restartListening();
            }, 1000);
          }
          break;
      }
    };

    recognition.onend = () => {
      console.log('音声認識終了');
      
      // 意図的な停止でない場合は自動再開
      if (isListening && !error) {
        restartListening();
      }
    };
  }, [continuous, interimResults, language, maxAlternatives, isListening, error]);

  const startListening = useCallback(() => {
    if (!isSupported || !recognitionRef.current) {
      setError('音声認識がサポートされていません。Chrome、Edge、Safariをお使いください。');
      return;
    }

    // 既に開始されている場合は何もしない
    if (isListening) {
      console.log('⚠️ 音声認識は既に開始されています');
      return;
    }

    try {
      setIsListening(true);
      setError(null);
      console.log('🎤 音声認識を開始します...');

      // 認識が既に動作中でないことを確認
      if (recognitionRef.current.readyState === 'running') {
        console.log('⚠️ 音声認識は既に動作中です');
        return;
      }

      recognitionRef.current.start();
    } catch (err: any) {
      console.error('❌ 音声認識の開始に失敗:', err);

      // 特定のエラーメッセージの処理
      if (err.message?.includes('already started')) {
        console.log('⚠️ 音声認識は既に開始されています');
        setError(null);
        return;
      }

      setError(`音声認識の開始に失敗: ${err.message || '不明なエラー'}`);
      setIsListening(false);
    }
  }, [isSupported, isListening]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      setIsListening(false);
      recognitionRef.current.stop();
      
      // 再開タイマーをクリア
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
        restartTimeoutRef.current = null;
      }
    }
  }, []);

  const restartListening = useCallback(() => {
    if (!isListening) return;

    // 既存のタイマーをクリア
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
    }

    // 少し待ってから再開
    restartTimeoutRef.current = setTimeout(() => {
      if (isListening && recognitionRef.current) {
        try {
          // 認識が既に動作中でないことを確認
          if (recognitionRef.current.readyState !== 'running') {
            console.log('🔄 音声認識を再開します...');
            recognitionRef.current.start();
            setError(null); // エラーをクリア
          }
        } catch (err: any) {
          console.error('❌ 音声認識の再開に失敗:', err);

          // 特定のエラーの場合は再試行しない
          if (err.message?.includes('already started')) {
            console.log('⚠️ 音声認識は既に開始されています');
            return;
          }

          setError(`再開エラー: ${err.message || '不明なエラー'}`);
          setIsListening(false);
        }
      }
    }, 500); // 少し長めの待機時間
  }, [isListening]);

  const resetTranscript = useCallback(() => {
    setTranscript('');
    setInterimTranscript('');
  }, []);

  // クリーンアップ
  useEffect(() => {
    return () => {
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
      }
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  return {
    isListening,
    transcript,
    interimTranscript,
    isSupported,
    error,
    startListening,
    stopListening,
    resetTranscript,
    // 全テキスト（確定 + 暫定）
    fullTranscript: transcript + interimTranscript
  };
};