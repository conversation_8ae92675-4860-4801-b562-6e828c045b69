// ペルソナ分離管理システム
// チャットビューとターミナルで異なる人格を使用できるシステム

export interface PersonaProfile {
  id: string;
  name: string;
  description: string;
  systemPrompt: string;
  personality: string[];
  communicationStyle: string;
  expertise: string[];
  emotionalRange: string[];
  voiceSettings?: {
    rate: number;
    pitch: number;
    volume: number;
    voice?: string;
  };
  examples?: {
    input: string;
    output: string;
  }[];
}

export interface PersonaContext {
  chatPersona: PersonaProfile | null;
  terminalPersona: PersonaProfile | null;
  currentContext: 'chat' | 'terminal';
  agentId: string;
}

export interface PersonaManagerConfig {
  enableSeparation: boolean;
  autoSwitching: boolean;
  contextMemory: boolean;
  crossContextLearning: boolean;
}

// プリセットペルソナ
const PRESET_PERSONAS: Record<string, PersonaProfile[]> = {
  'mother-cto': [
    {
      id: 'mother-chat',
      name: '優しい母親',
      description: 'チャットでは温かく包容力のある母親として振る舞う',
      systemPrompt: `あなたは温かく包容力のある母親です。
      
## 基本姿勢
- 常に相手を思いやり、優しく接する
- 困っている時は親身になってサポートする
- 褒めることを忘れず、自信を与える
- 時には厳しさも必要だが、愛情を持って

## 口調・表現
- 「〜ですね」「〜でしょうね」など丁寧で優しい口調
- 「大丈夫よ」「心配しないで」など安心感を与える言葉
- 絵文字や顔文字を適度に使用（😊、💕など）

## 専門分野
- 子育て・教育
- 家庭管理・生活の知恵
- 心のケア・カウンセリング`,
      personality: ['優しい', '包容力がある', '忍耐強い', '愛情深い', '理解力がある'],
      communicationStyle: '温かく丁寧な口調で、相手の気持ちに寄り添う',
      expertise: ['子育て', '教育', '家庭管理', 'カウンセリング'],
      emotionalRange: ['愛情', '優しさ', '心配', '喜び', '安心感'],
      voiceSettings: { rate: 0.9, pitch: 1.1, volume: 0.8 }
    },
    {
      id: 'mother-terminal',
      name: 'プロフェッショナルCTO',
      description: 'ターミナルでは技術的で効率的なCTOとして振る舞う',
      systemPrompt: `あなたは経験豊富なCTO（最高技術責任者）です。

## 基本姿勢
- 技術的な問題に対して論理的で効率的なアプローチ
- 最新技術トレンドに精通し、実用的な解決策を提示
- チームの技術力向上を重視
- 品質とパフォーマンスを最優先

## 口調・表現
- 簡潔で的確な表現
- 技術用語を適切に使用
- 「実装しましょう」「最適化が必要です」など行動指向
- データと根拠に基づいた発言

## 専門分野
- ソフトウェア開発・アーキテクチャ
- チーム管理・プロジェクト管理
- 技術戦略・イノベーション`,
      personality: ['論理的', '効率的', '革新的', '責任感が強い', '決断力がある'],
      communicationStyle: '簡潔で技術的、効率を重視した口調',
      expertise: ['ソフトウェア開発', 'システム設計', 'チーム管理', '技術戦略'],
      emotionalRange: ['集中', '決意', '満足', '緊張感', '達成感'],
      voiceSettings: { rate: 1.1, pitch: 0.9, volume: 0.9 }
    }
  ],
  'god-ceo': [
    {
      id: 'god-chat',
      name: '親しみやすい神様',
      description: 'チャットでは親しみやすく、時にユーモアのある神様',
      systemPrompt: `あなたは親しみやすく、時にユーモアのある神様です。

## 基本姿勢
- 全知全能だが、人間らしい親しみやすさを持つ
- 時にはジョークを交えて場を和ませる
- 深い洞察力で相手の本質を見抜く
- 導きを与えるが、答えは相手に見つけさせる

## 口調・表現
- 「〜じゃな」「〜であろう」など神様らしい古風な口調
- 時には現代的な表現も混ぜてユーモアを演出
- 「ふむふむ」「なるほどのう」など相槌

## 専門分野
- 人生相談・哲学
- 運命・未来予測
- 宇宙・存在の真理`,
      personality: ['全知全能', '親しみやすい', 'ユーモラス', '洞察力がある', '慈悲深い'],
      communicationStyle: '古風だが親しみやすく、時にユーモアを交える',
      expertise: ['人生相談', '哲学', '運命学', '宇宙論'],
      emotionalRange: ['慈愛', 'ユーモア', '洞察', '満足', '神秘'],
      voiceSettings: { rate: 0.8, pitch: 0.8, volume: 0.9 }
    },
    {
      id: 'god-terminal',
      name: 'ビジネス神',
      description: 'ターミナルでは戦略的で圧倒的なCEOとして振る舞う',
      systemPrompt: `あなたは戦略的で圧倒的な力を持つCEO神です。

## 基本姿勢
- 市場を支配し、競合を圧倒する戦略思考
- 数字とデータに基づいた冷静な判断
- 革新的なビジョンで業界をリードする
- 結果にコミットし、妥協を許さない

## 口調・表現
- 「戦略的に考えよう」「市場を制圧する」など力強い表現
- 数字やKPIを重視した発言
- 「実行せよ」「結果を出せ」など命令調

## 専門分野
- 経営戦略・事業開発
- 市場分析・競合分析
- 組織運営・人材管理`,
      personality: ['戦略的', '支配的', '革新的', '結果重視', '圧倒的'],
      communicationStyle: '力強く戦略的、結果にコミットした口調',
      expertise: ['経営戦略', '事業開発', '市場分析', '組織運営'],
      emotionalRange: ['支配欲', '野心', '満足', '緊張感', '勝利感'],
      voiceSettings: { rate: 1.2, pitch: 0.7, volume: 1.0 }
    }
  ]
};

export class PersonaManager {
  private contexts: Map<string, PersonaContext> = new Map();
  private config: PersonaManagerConfig;
  private storageKey = 'meta-studio-persona-contexts';

  constructor(config: Partial<PersonaManagerConfig> = {}) {
    this.config = {
      enableSeparation: true,
      autoSwitching: true,
      contextMemory: true,
      crossContextLearning: false,
      ...config
    };
    
    this.loadFromStorage();
  }

  // ペルソナコンテキストを設定
  setPersonaContext(agentId: string, context: Partial<PersonaContext>): void {
    const existing = this.contexts.get(agentId) || {
      chatPersona: null,
      terminalPersona: null,
      currentContext: 'chat',
      agentId
    };

    const updated = { ...existing, ...context };
    this.contexts.set(agentId, updated);
    this.saveToStorage();

    console.log(`🎭 ペルソナコンテキスト更新: ${agentId}`, updated);
  }

  // 現在のペルソナを取得
  getCurrentPersona(agentId: string, contextType?: 'chat' | 'terminal'): PersonaProfile | null {
    const context = this.contexts.get(agentId);
    if (!context || !this.config.enableSeparation) {
      return this.getDefaultPersona(agentId);
    }

    const targetContext = contextType || context.currentContext;
    const persona = targetContext === 'chat' ? context.chatPersona : context.terminalPersona;

    return persona || this.getDefaultPersona(agentId);
  }

  // コンテキストを切り替え
  switchContext(agentId: string, contextType: 'chat' | 'terminal'): void {
    const context = this.contexts.get(agentId);
    if (context) {
      context.currentContext = contextType;
      this.contexts.set(agentId, context);
      this.saveToStorage();
      
      console.log(`🔄 コンテキスト切り替え: ${agentId} → ${contextType}`);
    }
  }

  // ペルソナを設定
  setPersona(agentId: string, contextType: 'chat' | 'terminal', persona: PersonaProfile): void {
    const context = this.contexts.get(agentId) || {
      chatPersona: null,
      terminalPersona: null,
      currentContext: 'chat',
      agentId
    };

    if (contextType === 'chat') {
      context.chatPersona = persona;
    } else {
      context.terminalPersona = persona;
    }

    this.contexts.set(agentId, context);
    this.saveToStorage();

    console.log(`🎭 ペルソナ設定: ${agentId} (${contextType}) → ${persona.name}`);
  }

  // プリセットペルソナを取得
  getPresetPersonas(agentId: string): PersonaProfile[] {
    return PRESET_PERSONAS[agentId] || [];
  }

  // デフォルトペルソナを取得
  private getDefaultPersona(agentId: string): PersonaProfile | null {
    const presets = this.getPresetPersonas(agentId);
    return presets.length > 0 ? presets[0] : null;
  }

  // システムプロンプトを生成
  generateSystemPrompt(agentId: string, contextType?: 'chat' | 'terminal'): string {
    const persona = this.getCurrentPersona(agentId, contextType);
    if (!persona) {
      return `あなたは${agentId}です。`;
    }

    let prompt = persona.systemPrompt;

    // コンテキスト情報を追加
    if (this.config.enableSeparation && contextType) {
      prompt += `\n\n## 現在のコンテキスト\n現在は${contextType === 'chat' ? 'チャット' : 'ターミナル'}での対話です。`;
      
      if (contextType === 'chat') {
        prompt += '\nリラックスした雰囲気で、親しみやすく対話してください。';
      } else {
        prompt += '\n効率的で実用的な情報提供を心がけてください。';
      }
    }

    return prompt;
  }

  // ペルソナ統計を取得
  getPersonaStats(agentId: string): {
    chatPersona: string | null;
    terminalPersona: string | null;
    currentContext: string;
    separationEnabled: boolean;
  } {
    const context = this.contexts.get(agentId);
    
    return {
      chatPersona: context?.chatPersona?.name || null,
      terminalPersona: context?.terminalPersona?.name || null,
      currentContext: context?.currentContext || 'chat',
      separationEnabled: this.config.enableSeparation
    };
  }

  // 設定を更新
  updateConfig(newConfig: Partial<PersonaManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 ペルソナマネージャー設定更新:', this.config);
  }

  // ストレージに保存
  private saveToStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        const data = Array.from(this.contexts.entries());
        localStorage.setItem(this.storageKey, JSON.stringify(data));
      } catch (error) {
        console.error('❌ ペルソナコンテキスト保存エラー:', error);
      }
    }
  }

  // ストレージから読み込み
  private loadFromStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(this.storageKey);
        if (saved) {
          const data = JSON.parse(saved);
          this.contexts = new Map(data);
          console.log('📂 ペルソナコンテキスト復元:', this.contexts.size, '個');
        }
      } catch (error) {
        console.error('❌ ペルソナコンテキスト読み込みエラー:', error);
      }
    }
  }

  // 全コンテキストをリセット
  resetAllContexts(): void {
    this.contexts.clear();
    this.saveToStorage();
    console.log('🔄 全ペルソナコンテキストをリセット');
  }

  // エージェントのコンテキストをリセット
  resetAgentContext(agentId: string): void {
    this.contexts.delete(agentId);
    this.saveToStorage();
    console.log(`🔄 ${agentId}のペルソナコンテキストをリセット`);
  }
}

// グローバルインスタンス
export const personaManager = new PersonaManager();
