// Text-to-Speech (TTS) ユーティリティ

export interface TTSOptions {
  voice?: string;
  rate?: number;
  pitch?: number;
  volume?: number;
  lang?: string;
}

export interface TTSVoice {
  name: string;
  lang: string;
  gender: 'male' | 'female' | 'neutral';
  localService: boolean;
}

export class TextToSpeechManager {
  private synthesis: SpeechSynthesis | null = null;
  private voices: SpeechSynthesisVoice[] = [];
  private isEnabled: boolean = false;
  private currentUtterance: SpeechSynthesisUtterance | null = null;

  constructor() {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      this.synthesis = window.speechSynthesis;
      this.loadVoices();
      
      // 音声リストの読み込み完了を待つ
      if (this.synthesis.onvoiceschanged !== undefined) {
        this.synthesis.onvoiceschanged = () => {
          this.loadVoices();
        };
      }
    }
  }

  private loadVoices(): void {
    if (this.synthesis) {
      this.voices = this.synthesis.getVoices();
      console.log('🔊 利用可能な音声:', this.voices.length, '個');
    }
  }

  // TTS機能の有効/無効を切り替え
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (!enabled && this.currentUtterance) {
      this.stop();
    }
    
    // LocalStorageに保存
    if (typeof window !== 'undefined') {
      localStorage.setItem('tts-enabled', enabled.toString());
    }
  }

  // TTS機能の状態を取得
  isEnabledState(): boolean {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('tts-enabled');
      if (saved !== null) {
        this.isEnabled = saved === 'true';
      }
    }
    return this.isEnabled;
  }

  // 利用可能な音声リストを取得
  getAvailableVoices(): TTSVoice[] {
    return this.voices.map(voice => ({
      name: voice.name,
      lang: voice.lang,
      gender: this.detectGender(voice.name),
      localService: voice.localService
    }));
  }

  // 音声の性別を推測（名前から）
  private detectGender(voiceName: string): 'male' | 'female' | 'neutral' {
    const name = voiceName.toLowerCase();
    if (name.includes('female') || name.includes('woman') || name.includes('girl') || 
        name.includes('kyoko') || name.includes('otoya') || name.includes('haruka')) {
      return 'female';
    }
    if (name.includes('male') || name.includes('man') || name.includes('boy') ||
        name.includes('takeru') || name.includes('hikari')) {
      return 'male';
    }
    return 'neutral';
  }

  // 日本語に適した音声を取得
  getJapaneseVoices(): TTSVoice[] {
    return this.getAvailableVoices().filter(voice => 
      voice.lang.startsWith('ja') || voice.lang.includes('JP')
    );
  }

  // 英語に適した音声を取得
  getEnglishVoices(): TTSVoice[] {
    return this.getAvailableVoices().filter(voice => 
      voice.lang.startsWith('en') || voice.lang.includes('US') || voice.lang.includes('GB')
    );
  }

  // テキストを音声で読み上げ
  speak(text: string, options: TTSOptions = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.synthesis || !this.isEnabled) {
        resolve();
        return;
      }

      // 現在の読み上げを停止
      this.stop();

      // HTMLタグを除去
      const cleanText = text.replace(/<[^>]*>/g, '').trim();
      if (!cleanText) {
        resolve();
        return;
      }

      const utterance = new SpeechSynthesisUtterance(cleanText);
      
      // 音声設定
      utterance.rate = options.rate || 1.0;
      utterance.pitch = options.pitch || 1.0;
      utterance.volume = options.volume || 0.8;
      utterance.lang = options.lang || 'ja-JP';

      // 指定された音声を設定
      if (options.voice) {
        const voice = this.voices.find(v => v.name === options.voice);
        if (voice) {
          utterance.voice = voice;
        }
      } else {
        // デフォルトで日本語音声を選択
        const japaneseVoice = this.voices.find(v => 
          v.lang.startsWith('ja') && v.localService
        );
        if (japaneseVoice) {
          utterance.voice = japaneseVoice;
        }
      }

      // イベントハンドラー
      utterance.onend = () => {
        this.currentUtterance = null;
        resolve();
      };

      utterance.onerror = (event) => {
        this.currentUtterance = null;
        // "interrupted"エラーは正常な停止として扱う
        if (event.error === 'interrupted') {
          console.log('🔇 TTS 停止:', event.error);
          resolve();
        } else {
          console.error('TTS エラー:', event.error);
          reject(new Error(`TTS Error: ${event.error}`));
        }
      };

      utterance.onstart = () => {
        console.log('🔊 TTS 開始:', cleanText.slice(0, 50) + '...');
      };

      this.currentUtterance = utterance;
      this.synthesis.speak(utterance);
    });
  }

  // 読み上げを停止
  stop(): void {
    if (this.synthesis) {
      this.synthesis.cancel();
      this.currentUtterance = null;
    }
  }

  // 読み上げを一時停止
  pause(): void {
    if (this.synthesis && this.synthesis.speaking) {
      this.synthesis.pause();
    }
  }

  // 読み上げを再開
  resume(): void {
    if (this.synthesis && this.synthesis.paused) {
      this.synthesis.resume();
    }
  }

  // 現在読み上げ中かどうか
  isSpeaking(): boolean {
    return this.synthesis ? this.synthesis.speaking : false;
  }

  // 一時停止中かどうか
  isPaused(): boolean {
    return this.synthesis ? this.synthesis.paused : false;
  }

  // エージェント用の音声設定を取得
  getAgentVoiceSettings(agentId: string): TTSOptions {
    const settings: Record<string, TTSOptions> = {
      'mother-cto': {
        voice: this.getJapaneseVoices().find(v => v.gender === 'female')?.name,
        rate: 0.9,
        pitch: 1.1,
        lang: 'ja-JP'
      },
      'god-ceo': {
        voice: this.getJapaneseVoices().find(v => v.gender === 'male')?.name,
        rate: 1.1,
        pitch: 0.9,
        lang: 'ja-JP'
      },
      'king-coo': {
        voice: this.getJapaneseVoices().find(v => v.gender === 'male')?.name,
        rate: 1.0,
        pitch: 0.8,
        lang: 'ja-JP'
      },
      'general-cmo': {
        voice: this.getJapaneseVoices().find(v => v.gender === 'male')?.name,
        rate: 1.2,
        pitch: 0.9,
        lang: 'ja-JP'
      },
      'sage-cfo': {
        voice: this.getJapaneseVoices().find(v => v.gender === 'female')?.name,
        rate: 0.8,
        pitch: 1.0,
        lang: 'ja-JP'
      }
    };

    return settings[agentId] || {
      rate: 1.0,
      pitch: 1.0,
      lang: 'ja-JP'
    };
  }
}

// グローバルインスタンス
export const ttsManager = new TextToSpeechManager();
