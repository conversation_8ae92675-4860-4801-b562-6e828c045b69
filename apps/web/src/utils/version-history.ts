// Meta Studio バージョン履歴管理システム
// 0.0.1刻みでの変更履歴を自動・手動で管理

export interface VersionEntry {
  version: string;
  date: string;
  type: 'major' | 'minor' | 'patch' | 'hotfix';
  title: string;
  description: string;
  changes: string[];
  author: string;
  automated: boolean;
}

export interface VersionHistoryConfig {
  autoIncrement: boolean;
  notifyOnUpdate: boolean;
  saveToClaudeMd: boolean;
  maxHistoryEntries: number;
}

const DEFAULT_CONFIG: VersionHistoryConfig = {
  autoIncrement: true,
  notifyOnUpdate: true,
  saveToClaudeMd: true,
  maxHistoryEntries: 100
};

export class VersionHistoryManager {
  private storageKey = 'meta-studio-version-history';
  private currentVersionKey = 'meta-studio-current-version';
  private config: VersionHistoryConfig;
  private history: VersionEntry[] = [];
  private currentVersion: string = '0.8.0';

  constructor(config: Partial<VersionHistoryConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.loadFromStorage();
  }

  // 現在のバージョンを取得
  getCurrentVersion(): string {
    return this.currentVersion;
  }

  // バージョン履歴を取得
  getHistory(): VersionEntry[] {
    return [...this.history].sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  }

  // 新しいバージョンエントリを追加
  addVersion(entry: Omit<VersionEntry, 'version' | 'date' | 'automated'>): string {
    const newVersion = this.config.autoIncrement 
      ? this.incrementVersion(entry.type)
      : this.currentVersion;

    const versionEntry: VersionEntry = {
      ...entry,
      version: newVersion,
      date: new Date().toISOString(),
      automated: this.config.autoIncrement
    };

    this.history.unshift(versionEntry);
    this.currentVersion = newVersion;

    // 履歴数制限
    if (this.history.length > this.config.maxHistoryEntries) {
      this.history = this.history.slice(0, this.config.maxHistoryEntries);
    }

    this.saveToStorage();

    if (this.config.saveToClaudeMd) {
      this.updateClaudeMd(versionEntry);
    }

    if (this.config.notifyOnUpdate) {
      this.notifyVersionUpdate(versionEntry);
    }

    console.log(`📝 バージョン更新: ${newVersion} - ${entry.title}`);
    return newVersion;
  }

  // バージョンを自動インクリメント
  private incrementVersion(type: VersionEntry['type']): string {
    const parts = this.currentVersion.split('.').map(Number);
    let [major, minor, patch] = parts;

    switch (type) {
      case 'major':
        major += 1;
        minor = 0;
        patch = 0;
        break;
      case 'minor':
        minor += 1;
        patch = 0;
        break;
      case 'patch':
      case 'hotfix':
        patch += 1;
        break;
    }

    return `${major}.${minor}.${patch}`;
  }

  // 手動でバージョンを設定
  setVersion(version: string): void {
    this.currentVersion = version;
    this.saveToStorage();
  }

  // 特定のバージョンエントリを取得
  getVersion(version: string): VersionEntry | null {
    return this.history.find(entry => entry.version === version) || null;
  }

  // バージョン間の変更を比較
  compareVersions(fromVersion: string, toVersion: string): {
    from: VersionEntry | null;
    to: VersionEntry | null;
    changes: string[];
  } {
    const from = this.getVersion(fromVersion);
    const to = this.getVersion(toVersion);
    
    const changes: string[] = [];
    if (from && to) {
      const fromIndex = this.history.findIndex(v => v.version === fromVersion);
      const toIndex = this.history.findIndex(v => v.version === toVersion);
      
      if (fromIndex > toIndex) {
        // fromからtoまでの変更を収集
        for (let i = toIndex; i < fromIndex; i++) {
          changes.push(...this.history[i].changes);
        }
      }
    }

    return { from, to, changes };
  }

  // 統計情報を取得
  getStatistics(): {
    totalVersions: number;
    versionsByType: Record<string, number>;
    averageChangesPerVersion: number;
    lastUpdateDate: string;
    developmentDays: number;
  } {
    const versionsByType = this.history.reduce((acc, entry) => {
      acc[entry.type] = (acc[entry.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalChanges = this.history.reduce((sum, entry) => sum + entry.changes.length, 0);
    const averageChangesPerVersion = this.history.length > 0 ? totalChanges / this.history.length : 0;

    const lastUpdateDate = this.history.length > 0 ? this.history[0].date : '';
    
    const firstEntry = this.history[this.history.length - 1];
    const developmentDays = firstEntry 
      ? Math.ceil((Date.now() - new Date(firstEntry.date).getTime()) / (1000 * 60 * 60 * 24))
      : 0;

    return {
      totalVersions: this.history.length,
      versionsByType,
      averageChangesPerVersion: Math.round(averageChangesPerVersion * 10) / 10,
      lastUpdateDate,
      developmentDays
    };
  }

  // プリセットバージョンエントリを追加
  addPresetVersion(preset: 'feature' | 'bugfix' | 'ui' | 'performance' | 'docs', 
                   title: string, 
                   changes: string[]): string {
    const presets = {
      feature: { type: 'minor' as const, author: 'Meta Studio Dev Team' },
      bugfix: { type: 'patch' as const, author: 'Meta Studio Dev Team' },
      ui: { type: 'patch' as const, author: 'UI/UX Team' },
      performance: { type: 'patch' as const, author: 'Performance Team' },
      docs: { type: 'patch' as const, author: 'Documentation Team' }
    };

    const presetConfig = presets[preset];
    return this.addVersion({
      type: presetConfig.type,
      title,
      description: `${preset}の更新: ${title}`,
      changes,
      author: presetConfig.author
    });
  }

  // claude.mdファイルを更新
  private async updateClaudeMd(entry: VersionEntry): Promise<void> {
    try {
      const claudeMdEntry = this.formatClaudeMdEntry(entry);
      console.log('📄 claude.md更新エントリ:', claudeMdEntry);
      
      // 実際のファイル更新は外部で実行される想定
      // ここではログ出力のみ
    } catch (error) {
      console.error('❌ claude.md更新エラー:', error);
    }
  }

  // claude.md用のフォーマット
  private formatClaudeMdEntry(entry: VersionEntry): string {
    const date = new Date(entry.date).toLocaleDateString('ja-JP');
    return `
### ${entry.version} (${date}) - ${entry.title}
**タイプ**: ${entry.type} | **作成者**: ${entry.author}
**説明**: ${entry.description}

**変更内容**:
${entry.changes.map(change => `- ${change}`).join('\n')}
`;
  }

  // バージョン更新通知
  private notifyVersionUpdate(entry: VersionEntry): void {
    if (typeof window !== 'undefined' && (window as any).notify) {
      (window as any).notify.success(
        `バージョン更新: ${entry.version}`,
        entry.title,
        { duration: 5000 }
      );
    }
  }

  // ストレージに保存
  private saveToStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(this.storageKey, JSON.stringify(this.history));
        localStorage.setItem(this.currentVersionKey, this.currentVersion);
      } catch (error) {
        console.error('❌ バージョン履歴保存エラー:', error);
      }
    }
  }

  // ストレージから読み込み
  private loadFromStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        const savedHistory = localStorage.getItem(this.storageKey);
        const savedVersion = localStorage.getItem(this.currentVersionKey);
        
        if (savedHistory) {
          this.history = JSON.parse(savedHistory);
        }
        
        if (savedVersion) {
          this.currentVersion = savedVersion;
        }
        
        console.log(`📚 バージョン履歴読み込み: ${this.history.length}件, 現在: ${this.currentVersion}`);
      } catch (error) {
        console.error('❌ バージョン履歴読み込みエラー:', error);
      }
    }
  }

  // 履歴をエクスポート
  exportHistory(): string {
    return JSON.stringify({
      currentVersion: this.currentVersion,
      history: this.history,
      exportDate: new Date().toISOString(),
      config: this.config
    }, null, 2);
  }

  // 履歴をインポート
  importHistory(data: string): boolean {
    try {
      const parsed = JSON.parse(data);
      if (parsed.history && Array.isArray(parsed.history)) {
        this.history = parsed.history;
        this.currentVersion = parsed.currentVersion || this.currentVersion;
        this.saveToStorage();
        console.log('📥 バージョン履歴インポート完了');
        return true;
      }
    } catch (error) {
      console.error('❌ バージョン履歴インポートエラー:', error);
    }
    return false;
  }

  // 設定を更新
  updateConfig(newConfig: Partial<VersionHistoryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ バージョン履歴設定更新:', this.config);
  }
}

// グローバルインスタンス
export const versionHistory = new VersionHistoryManager();

// 便利な関数
export const addVersion = (
  type: VersionEntry['type'],
  title: string,
  changes: string[],
  description?: string
) => {
  return versionHistory.addVersion({
    type,
    title,
    description: description || title,
    changes,
    author: 'Meta Studio Dev Team'
  });
};

// 現在のセッションで実装した機能を自動記録
export const recordSessionChanges = () => {
  const sessionChanges = [
    'UIデザイン統一化（ヘッダー、ボタン、インジケーター）',
    '音声認識エラーハンドリング大幅改善',
    'VRM永続化システム（IndexedDB）実装',
    'プレビュー形式モデル選択UI実装',
    'ペルソナ分離システム実装',
    '拡張ファイルエクスプローラー実装',
    '統合通知システム実装',
    'バージョン履歴管理システム実装'
  ];

  return addVersion(
    'minor',
    'UI/UX改善とシステム機能拡張',
    sessionChanges,
    '統一されたUIデザインと高度なシステム機能の実装'
  );
};
