// ストレージクリーンアップユーティリティ
// 古いデータや存在しないプロジェクトデータを削除

export interface StorageCleanupOptions {
  clearProjects?: boolean;
  clearVRM?: boolean;
  clearPersonas?: boolean;
  clearVersionHistory?: boolean;
  clearAll?: boolean;
}

export class StorageCleanup {
  private static readonly STORAGE_KEYS = {
    projects: 'meta-studio-projects',
    vrmData: 'meta-studio-vrm-data',
    personas: 'meta-studio-personas',
    versionHistory: 'meta-studio-version-history',
    currentVersion: 'meta-studio-current-version',
    settings: 'meta-studio-settings'
  };

  // 特定のストレージキーをクリア
  static clearStorageKey(key: string): boolean {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem(key);
        console.log(`🧹 ストレージキー削除: ${key}`);
        return true;
      }
    } catch (error) {
      console.error(`❌ ストレージキー削除エラー: ${key}`, error);
    }
    return false;
  }

  // 複数のストレージキーをクリア
  static clearStorageKeys(keys: string[]): number {
    let clearedCount = 0;
    keys.forEach(key => {
      if (this.clearStorageKey(key)) {
        clearedCount++;
      }
    });
    return clearedCount;
  }

  // 選択的ストレージクリーンアップ
  static cleanupStorage(options: StorageCleanupOptions = {}): {
    success: boolean;
    clearedKeys: string[];
    errors: string[];
  } {
    const clearedKeys: string[] = [];
    const errors: string[] = [];

    try {
      if (options.clearAll) {
        // 全てのMeta Studio関連データをクリア
        Object.values(this.STORAGE_KEYS).forEach(key => {
          if (this.clearStorageKey(key)) {
            clearedKeys.push(key);
          } else {
            errors.push(key);
          }
        });
      } else {
        // 選択的クリア
        if (options.clearProjects) {
          if (this.clearStorageKey(this.STORAGE_KEYS.projects)) {
            clearedKeys.push(this.STORAGE_KEYS.projects);
          } else {
            errors.push(this.STORAGE_KEYS.projects);
          }
        }

        if (options.clearVRM) {
          if (this.clearStorageKey(this.STORAGE_KEYS.vrmData)) {
            clearedKeys.push(this.STORAGE_KEYS.vrmData);
          } else {
            errors.push(this.STORAGE_KEYS.vrmData);
          }
        }

        if (options.clearPersonas) {
          if (this.clearStorageKey(this.STORAGE_KEYS.personas)) {
            clearedKeys.push(this.STORAGE_KEYS.personas);
          } else {
            errors.push(this.STORAGE_KEYS.personas);
          }
        }

        if (options.clearVersionHistory) {
          if (this.clearStorageKey(this.STORAGE_KEYS.versionHistory)) {
            clearedKeys.push(this.STORAGE_KEYS.versionHistory);
          }
          if (this.clearStorageKey(this.STORAGE_KEYS.currentVersion)) {
            clearedKeys.push(this.STORAGE_KEYS.currentVersion);
          }
        }
      }

      console.log(`🧹 ストレージクリーンアップ完了: ${clearedKeys.length}件削除`);
      return {
        success: errors.length === 0,
        clearedKeys,
        errors
      };

    } catch (error) {
      console.error('❌ ストレージクリーンアップエラー:', error);
      return {
        success: false,
        clearedKeys,
        errors: ['全般的なエラー']
      };
    }
  }

  // 古いプロジェクトデータを削除
  static cleanupOldProjects(): boolean {
    try {
      const oldProjectKeys = [
        'meditation-app',
        'trading-bot',
        'is-streamer',
        'meta-studio-projects'
      ];

      oldProjectKeys.forEach(key => {
        this.clearStorageKey(key);
      });

      console.log('🧹 古いプロジェクトデータクリーンアップ完了');
      return true;
    } catch (error) {
      console.error('❌ 古いプロジェクトデータクリーンアップエラー:', error);
      return false;
    }
  }

  // 破損したデータを検出・修復
  static detectAndFixCorruptedData(): {
    detected: string[];
    fixed: string[];
    errors: string[];
  } {
    const detected: string[] = [];
    const fixed: string[] = [];
    const errors: string[] = [];

    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        // 各ストレージキーをチェック
        Object.entries(this.STORAGE_KEYS).forEach(([name, key]) => {
          try {
            const data = localStorage.getItem(key);
            if (data) {
              // JSONパース可能かチェック
              JSON.parse(data);
            }
          } catch (parseError) {
            detected.push(key);
            // 破損したデータを削除
            if (this.clearStorageKey(key)) {
              fixed.push(key);
            } else {
              errors.push(key);
            }
          }
        });
      }

      if (detected.length > 0) {
        console.log(`🔧 破損データ検出・修復: ${detected.length}件検出, ${fixed.length}件修復`);
      }

      return { detected, fixed, errors };
    } catch (error) {
      console.error('❌ データ破損検出エラー:', error);
      return { detected, fixed, errors: ['検出処理エラー'] };
    }
  }

  // ストレージ使用量を取得
  static getStorageUsage(): {
    total: number;
    metaStudio: number;
    keys: Record<string, number>;
  } {
    const usage = {
      total: 0,
      metaStudio: 0,
      keys: {} as Record<string, number>
    };

    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        // 全体の使用量
        let totalSize = 0;
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            const value = localStorage.getItem(key) || '';
            const size = new Blob([key + value]).size;
            totalSize += size;

            // Meta Studio関連のキーかチェック
            if (key.startsWith('meta-studio-') || Object.values(this.STORAGE_KEYS).includes(key)) {
              usage.metaStudio += size;
              usage.keys[key] = size;
            }
          }
        }
        usage.total = totalSize;
      }
    } catch (error) {
      console.error('❌ ストレージ使用量取得エラー:', error);
    }

    return usage;
  }

  // ストレージ情報をレポート
  static generateStorageReport(): string {
    const usage = this.getStorageUsage();
    const corruptionCheck = this.detectAndFixCorruptedData();

    const formatBytes = (bytes: number): string => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    let report = '# Meta Studio ストレージレポート\n\n';
    report += `## 使用量\n`;
    report += `- **総使用量**: ${formatBytes(usage.total)}\n`;
    report += `- **Meta Studio使用量**: ${formatBytes(usage.metaStudio)}\n\n`;

    if (Object.keys(usage.keys).length > 0) {
      report += `## Meta Studio関連キー\n`;
      Object.entries(usage.keys).forEach(([key, size]) => {
        report += `- **${key}**: ${formatBytes(size)}\n`;
      });
      report += '\n';
    }

    if (corruptionCheck.detected.length > 0) {
      report += `## データ破損検出\n`;
      report += `- **検出件数**: ${corruptionCheck.detected.length}\n`;
      report += `- **修復件数**: ${corruptionCheck.fixed.length}\n`;
      report += `- **エラー件数**: ${corruptionCheck.errors.length}\n\n`;
    }

    report += `## 推奨アクション\n`;
    if (usage.metaStudio > 1024 * 1024) { // 1MB以上
      report += `- ストレージ使用量が多いため、不要なデータの削除を検討\n`;
    }
    if (corruptionCheck.detected.length > 0) {
      report += `- 破損データが検出されました。修復を実行してください\n`;
    }
    report += `- 定期的なストレージクリーンアップを推奨\n`;

    return report;
  }

  // 緊急クリーンアップ（全データ削除）
  static emergencyCleanup(): boolean {
    try {
      const result = this.cleanupStorage({ clearAll: true });
      
      // ページリロードを促す
      if (result.success && typeof window !== 'undefined') {
        console.log('🚨 緊急クリーンアップ完了。ページをリロードしてください。');
        if (confirm('緊急クリーンアップが完了しました。ページをリロードしますか？')) {
          window.location.reload();
        }
      }

      return result.success;
    } catch (error) {
      console.error('❌ 緊急クリーンアップエラー:', error);
      return false;
    }
  }
}

// 便利な関数をエクスポート
export const cleanupOldProjects = () => StorageCleanup.cleanupOldProjects();
export const emergencyCleanup = () => StorageCleanup.emergencyCleanup();
export const generateStorageReport = () => StorageCleanup.generateStorageReport();

// 自動実行：ページロード時に古いプロジェクトデータをクリーンアップ
if (typeof window !== 'undefined') {
  // ページロード後に実行
  window.addEventListener('load', () => {
    setTimeout(() => {
      StorageCleanup.cleanupOldProjects();
    }, 1000);
  });
}
