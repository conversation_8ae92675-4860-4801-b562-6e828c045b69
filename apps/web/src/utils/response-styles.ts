// 回答スタイルシステム - 5つの個性的なスタイル

export interface ResponseStyle {
  id: string;
  name: string;
  description: string;
  emoji: string;
  systemPrompt: string;
  examples: string[];
  color: string;
}

export const RESPONSE_STYLES: ResponseStyle[] = [
  {
    id: 'standard',
    name: '標準',
    description: '丁寧で分かりやすい標準的な回答',
    emoji: '🤖',
    systemPrompt: '丁寧で分かりやすく、技術的に正確な回答をしてください。',
    examples: [
      'ご質問ありがとうございます。',
      'こちらの方法をお試しください。',
      '詳細な説明をいたします。'
    ],
    color: 'text-blue-500'
  },
  {
    id: 'gal',
    name: 'ギャル',
    description: 'ギャル語で楽しく親しみやすい回答',
    emoji: '💅',
    systemPrompt: 'ギャル語を使って、明るく楽しく、親しみやすい口調で回答してください。「〜だよ〜」「マジで」「ヤバい」「超」などの表現を使い、絵文字も多用してください。',
    examples: [
      'マジで〜？それヤバくない？✨',
      '超簡単だよ〜！やってみて💕',
      'あ〜それね〜！分かる分かる〜😊'
    ],
    color: 'text-pink-500'
  },
  {
    id: 'frank',
    name: 'フランク',
    description: 'カジュアルで親しみやすいフランクな回答',
    emoji: '😎',
    systemPrompt: 'カジュアルで親しみやすく、友達のような口調で回答してください。敬語は使わず、「だね」「〜じゃん」「まあ」などの表現を使ってください。',
    examples: [
      'あ〜、それね！簡単だよ',
      'まあ、こんな感じでやってみて',
      'うん、それで大丈夫じゃん'
    ],
    color: 'text-green-500'
  },
  {
    id: 'otaku',
    name: 'オタク',
    description: '専門用語満載の濃いオタク系回答',
    emoji: '🤓',
    systemPrompt: 'オタク的な知識を披露しながら、専門用語を多用して詳細に説明してください。「〜である」「実装的には」「アーキテクチャ的に」などの表現を使い、技術的な深掘りを好んでください。',
    examples: [
      '実装的には、このアーキテクチャが最適解である',
      'パフォーマンス的に考慮すべき点が複数存在する',
      'フレームワークの設計思想を理解する必要がある'
    ],
    color: 'text-purple-500'
  },
  {
    id: 'comedian',
    name: 'お笑い',
    description: 'ボケとツッコミを交えた面白い回答',
    emoji: '🎭',
    systemPrompt: 'お笑い芸人のように、ボケとツッコミを交えながら面白く回答してください。関西弁も使い、「なんでやねん」「ちゃうちゃう」「せやな」などの表現で笑いを取りつつ、しっかりと情報も伝えてください。',
    examples: [
      'なんでやねん！そんなん簡単やがな〜',
      'ちゃうちゃう、そうじゃなくて〜',
      'せやな〜、それでええんちゃう？'
    ],
    color: 'text-orange-500'
  },
  {
    id: 'custom',
    name: 'カスタム',
    description: '自由にプロンプトを設定できるカスタムモデル',
    emoji: '⚙️',
    systemPrompt: '', // カスタムプロンプトで上書きされる
    examples: [
      'カスタムプロンプトに従って回答',
      '自由な設定で個性的な回答',
      'ユーザー定義の性格で対応'
    ],
    color: 'text-gray-500'
  }
];

// 現在のスタイルを管理するクラス
export class ResponseStyleManager {
  private currentStyle: ResponseStyle;
  private storageKey: string;
  private customPrompt: string = '';

  constructor(storageKey: string = 'meta-studio-response-style') {
    this.storageKey = storageKey;
    this.currentStyle = this.loadSavedStyle();
    this.customPrompt = this.loadCustomPrompt();
  }

  // 保存されたスタイルを読み込み
  private loadSavedStyle(): ResponseStyle {
    if (typeof window === 'undefined') {
      return RESPONSE_STYLES[0]; // SSR対応
    }

    try {
      const saved = localStorage.getItem(this.storageKey);
      if (saved) {
        const styleId = JSON.parse(saved);
        const style = RESPONSE_STYLES.find(s => s.id === styleId);
        if (style) return style;
      }
    } catch (error) {
      console.warn('スタイル読み込みエラー:', error);
    }

    return RESPONSE_STYLES[0]; // デフォルトは標準
  }

  // スタイルを変更
  setStyle(styleId: string): boolean {
    const style = RESPONSE_STYLES.find(s => s.id === styleId);
    if (style) {
      this.currentStyle = style;
      this.saveStyle();
      return true;
    }
    return false;
  }

  // スタイルを保存
  private saveStyle(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(this.storageKey, JSON.stringify(this.currentStyle.id));
      } catch (error) {
        console.warn('スタイル保存エラー:', error);
      }
    }
  }

  // 現在のスタイルを取得
  getCurrentStyle(): ResponseStyle {
    return this.currentStyle;
  }

  // 利用可能なスタイル一覧を取得
  getAvailableStyles(): ResponseStyle[] {
    return RESPONSE_STYLES;
  }

  // カスタムプロンプトを設定
  setCustomPrompt(prompt: string): void {
    this.customPrompt = prompt;
    this.saveCustomPrompt();
  }

  // カスタムプロンプトを取得
  getCustomPrompt(): string {
    return this.customPrompt;
  }

  // カスタムプロンプトを保存
  private saveCustomPrompt(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(`${this.storageKey}-custom-prompt`, this.customPrompt);
      } catch (error) {
        console.warn('カスタムプロンプト保存エラー:', error);
      }
    }
  }

  // カスタムプロンプトを読み込み
  private loadCustomPrompt(): string {
    if (typeof window === 'undefined') {
      return '';
    }

    try {
      const saved = localStorage.getItem(`${this.storageKey}-custom-prompt`);
      return saved || '';
    } catch (error) {
      console.warn('カスタムプロンプト読み込みエラー:', error);
      return '';
    }
  }

  // プロンプトにスタイルを適用（役割指定形式）
  applyStyleToPrompt(originalPrompt: string): string {
    if (this.currentStyle.id === 'standard') {
      return originalPrompt; // 標準スタイルはそのまま
    }

    // カスタムスタイルの場合はカスタムプロンプトを使用
    const systemPrompt = this.currentStyle.id === 'custom' && this.customPrompt
      ? this.customPrompt
      : this.currentStyle.systemPrompt;

    // 役割・性格を明確に指定する形式
    return `あなたは以下の役割・性格で回答してください：

【役割・性格】: ${systemPrompt}

【ユーザーからの質問】: ${originalPrompt}

上記の役割・性格に従って、自然に回答してください。`;
  }

  // レスポンスにスタイルを適用（後処理）
  applyStyleToResponse(response: string): string {
    // 各スタイルに応じた後処理
    switch (this.currentStyle.id) {
      case 'gal':
        return this.addGalEmojis(response);
      case 'comedian':
        return this.addComedyElements(response);
      default:
        return response;
    }
  }

  private addGalEmojis(text: string): string {
    // ギャル語に絵文字を追加
    return text
      .replace(/！/g, '！✨')
      .replace(/。/g, '。💕')
      .replace(/\?/g, '？😊');
  }

  private addComedyElements(text: string): string {
    // お笑い要素を追加
    const comedyPhrases = ['（ツッコミ）', '（ボケ）', '（笑）'];
    const randomPhrase = comedyPhrases[Math.floor(Math.random() * comedyPhrases.length)];
    return text + ` ${randomPhrase}`;
  }
}

// グローバルインスタンス（チャット用とターミナル用を分離）
export const responseStyleManager = new ResponseStyleManager('meta-studio-chat-response-style');
export const terminalStyleManager = new ResponseStyleManager('meta-studio-terminal-response-style');
