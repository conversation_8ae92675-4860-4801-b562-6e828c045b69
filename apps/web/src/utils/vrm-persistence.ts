// VRMモデル永続化システム
// リロード後もVRMモデルの状態を保持するためのLocalStorage/IndexedDB実装

export interface VRMModelData {
  id: string;
  agentId: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  lastUsed: string;
  arrayBuffer: ArrayBuffer;
  metadata?: {
    title?: string;
    author?: string;
    version?: string;
    description?: string;
  };
}

export interface VRMPersistenceConfig {
  maxModels: number;
  maxFileSize: number; // bytes
  compressionEnabled: boolean;
  autoCleanup: boolean;
  cleanupDays: number;
}

// デフォルト設定
const DEFAULT_CONFIG: VRMPersistenceConfig = {
  maxModels: 10,
  maxFileSize: 50 * 1024 * 1024, // 50MB
  compressionEnabled: true,
  autoCleanup: true,
  cleanupDays: 30
};

export class VRMPersistenceManager {
  private dbName = 'VRMModelStorage';
  private dbVersion = 1;
  private storeName = 'models';
  private db: IDBDatabase | null = null;
  private config: VRMPersistenceConfig;

  constructor(config: Partial<VRMPersistenceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // IndexedDBを初期化
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!('indexedDB' in window)) {
        reject(new Error('IndexedDB is not supported'));
        return;
      }

      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('🗄️ VRM永続化DB初期化完了');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // オブジェクトストアを作成
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
          store.createIndex('agentId', 'agentId', { unique: false });
          store.createIndex('lastUsed', 'lastUsed', { unique: false });
          console.log('🗄️ VRMモデルストア作成完了');
        }
      };
    });
  }

  // VRMモデルを保存
  async saveModel(modelData: Omit<VRMModelData, 'id' | 'uploadDate' | 'lastUsed'>): Promise<string> {
    if (!this.db) {
      await this.initialize();
    }

    // ファイルサイズチェック
    if (modelData.arrayBuffer.byteLength > this.config.maxFileSize) {
      throw new Error(`ファイルサイズが上限(${this.config.maxFileSize / 1024 / 1024}MB)を超えています`);
    }

    const id = this.generateModelId(modelData.agentId, modelData.fileName);
    const now = new Date().toISOString();

    const fullModelData: VRMModelData = {
      ...modelData,
      id,
      uploadDate: now,
      lastUsed: now
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);

      const request = store.put(fullModelData);

      request.onsuccess = () => {
        console.log(`💾 VRMモデル保存完了: ${id}`);
        this.updateLastUsed(id);
        this.performCleanupIfNeeded();
        resolve(id);
      };

      request.onerror = () => {
        reject(new Error('VRMモデルの保存に失敗しました'));
      };
    });
  }

  // VRMモデルを取得
  async getModel(id: string): Promise<VRMModelData | null> {
    if (!this.db) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(id);

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          this.updateLastUsed(id);
          console.log(`📂 VRMモデル取得: ${id}`);
        }
        resolve(result || null);
      };

      request.onerror = () => {
        reject(new Error('VRMモデルの取得に失敗しました'));
      };
    });
  }

  // エージェント用のVRMモデルを取得
  async getModelByAgent(agentId: string): Promise<VRMModelData | null> {
    if (!this.db) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const index = store.index('agentId');
      const request = index.get(agentId);

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          this.updateLastUsed(result.id);
          console.log(`👤 エージェントVRMモデル取得: ${agentId} → ${result.id}`);
        }
        resolve(result || null);
      };

      request.onerror = () => {
        reject(new Error('エージェントVRMモデルの取得に失敗しました'));
      };
    });
  }

  // 全VRMモデルのリストを取得
  async getAllModels(): Promise<VRMModelData[]> {
    if (!this.db) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        reject(new Error('VRMモデルリストの取得に失敗しました'));
      };
    });
  }

  // VRMモデルを削除
  async deleteModel(id: string): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(id);

      request.onsuccess = () => {
        console.log(`🗑️ VRMモデル削除: ${id}`);
        resolve();
      };

      request.onerror = () => {
        reject(new Error('VRMモデルの削除に失敗しました'));
      };
    });
  }

  // 最終使用日時を更新
  private async updateLastUsed(id: string): Promise<void> {
    if (!this.db) return;

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);
    
    const getRequest = store.get(id);
    getRequest.onsuccess = () => {
      const model = getRequest.result;
      if (model) {
        model.lastUsed = new Date().toISOString();
        store.put(model);
      }
    };
  }

  // 自動クリーンアップ
  private async performCleanupIfNeeded(): Promise<void> {
    if (!this.config.autoCleanup) return;

    const models = await this.getAllModels();
    
    // 古いモデルを削除
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.cleanupDays);
    
    const oldModels = models.filter(model => 
      new Date(model.lastUsed) < cutoffDate
    );

    for (const model of oldModels) {
      await this.deleteModel(model.id);
    }

    // モデル数制限
    if (models.length > this.config.maxModels) {
      const sortedModels = models.sort((a, b) => 
        new Date(a.lastUsed).getTime() - new Date(b.lastUsed).getTime()
      );
      
      const modelsToDelete = sortedModels.slice(0, models.length - this.config.maxModels);
      for (const model of modelsToDelete) {
        await this.deleteModel(model.id);
      }
    }
  }

  // モデルIDを生成
  private generateModelId(agentId: string, fileName: string): string {
    const timestamp = Date.now();
    const hash = this.simpleHash(`${agentId}-${fileName}-${timestamp}`);
    return `vrm_${agentId}_${hash}`;
  }

  // 簡単なハッシュ関数
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 32bit整数に変換
    }
    return Math.abs(hash).toString(36);
  }

  // ストレージ使用量を取得
  async getStorageInfo(): Promise<{
    modelCount: number;
    totalSize: number;
    models: Array<{ id: string; agentId: string; fileName: string; size: number; lastUsed: string }>;
  }> {
    const models = await this.getAllModels();
    const totalSize = models.reduce((sum, model) => sum + model.arrayBuffer.byteLength, 0);

    return {
      modelCount: models.length,
      totalSize,
      models: models.map(model => ({
        id: model.id,
        agentId: model.agentId,
        fileName: model.fileName,
        size: model.arrayBuffer.byteLength,
        lastUsed: model.lastUsed
      }))
    };
  }

  // 設定を更新
  updateConfig(newConfig: Partial<VRMPersistenceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// グローバルインスタンス
export const vrmPersistence = new VRMPersistenceManager();
