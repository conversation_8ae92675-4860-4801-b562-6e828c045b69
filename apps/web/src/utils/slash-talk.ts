// スラッシュトーク音声入力機能
// 音声で「入力、スラッシュ、指示」を分けて伝える革新的音声インターフェース

export interface SlashTalkCommand {
  type: 'input' | 'command' | 'action';
  content: string;
  originalText: string;
}

export interface SlashTalkResult {
  commands: SlashTalkCommand[];
  processedText: string;
  hasCommands: boolean;
}

// スラッシュトーク解析クラス
export class SlashTalkProcessor {
  private slashKeywords = [
    'スラッシュ', 'すらっしゅ', 'スラ', 'すら',
    'コマンド', 'こまんど', 'コマ', 'こま',
    '指示', 'しじ', '命令', 'めいれい'
  ];

  private actionKeywords = {
    'send': ['送信', 'そうしん', '送って', 'おくって', '実行', 'じっこう'],
    'clear': ['クリア', 'くりあ', '消去', 'しょうきょ', '削除', 'さくじょ'],
    'copy': ['コピー', 'こぴー', '複製', 'ふくせい'],
    'paste': ['ペースト', 'ぺーすと', '貼り付け', 'はりつけ'],
    'save': ['保存', 'ほぞん', 'セーブ', 'せーぶ'],
    'undo': ['戻る', 'もどる', 'アンドゥ', 'あんどぅ', '取り消し', 'とりけし'],
    'redo': ['やり直し', 'やりなおし', 'リドゥ', 'りどぅ'],
    'search': ['検索', 'けんさく', '探す', 'さがす', 'サーチ', 'さーち'],
    'replace': ['置換', 'ちかん', '置き換え', 'おきかえ', 'リプレース', 'りぷれーす'],
    'format': ['フォーマット', 'ふぉーまっと', '整形', 'せいけい'],
    'translate': ['翻訳', 'ほんやく', '訳して', 'やくして'],
    'summarize': ['要約', 'ようやく', 'まとめ', 'まとめて'],
    'explain': ['説明', 'せつめい', '解説', 'かいせつ'],
    'analyze': ['分析', 'ぶんせき', '解析', 'かいせき'],
    'generate': ['生成', 'せいせい', '作成', 'さくせい', '作って', 'つくって'],
    'edit': ['編集', 'へんしゅう', '修正', 'しゅうせい'],
    'review': ['レビュー', 'れびゅー', '確認', 'かくにん', 'チェック', 'ちぇっく']
  };

  // 音声テキストを解析してスラッシュコマンドを抽出
  processVoiceInput(voiceText: string): SlashTalkResult {
    const normalizedText = this.normalizeText(voiceText);
    const commands: SlashTalkCommand[] = [];
    let processedText = normalizedText;
    let hasCommands = false;

    // スラッシュキーワードで分割
    const parts = this.splitBySlashKeywords(normalizedText);
    
    if (parts.length > 1) {
      hasCommands = true;
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i].trim();
        if (!part) continue;

        if (i === 0) {
          // 最初の部分は通常の入力テキスト
          commands.push({
            type: 'input',
            content: part,
            originalText: part
          });
        } else {
          // スラッシュ以降はコマンドとして解析
          const command = this.parseCommand(part);
          commands.push(command);
        }
      }

      // 処理済みテキストを生成
      processedText = this.generateProcessedText(commands);
    } else {
      // スラッシュコマンドがない場合は通常の入力として扱う
      commands.push({
        type: 'input',
        content: normalizedText,
        originalText: normalizedText
      });
    }

    return {
      commands,
      processedText,
      hasCommands
    };
  }

  // テキストの正規化
  private normalizeText(text: string): string {
    return text
      .replace(/\s+/g, ' ')  // 複数の空白を1つに
      .replace(/[。、]/g, '') // 句読点を削除
      .trim();
  }

  // スラッシュキーワードで分割
  private splitBySlashKeywords(text: string): string[] {
    let result = [text];
    
    for (const keyword of this.slashKeywords) {
      const newResult: string[] = [];
      
      for (const part of result) {
        const splits = part.split(new RegExp(`(${keyword})`, 'gi'));
        for (let i = 0; i < splits.length; i++) {
          if (splits[i] && !this.slashKeywords.includes(splits[i].toLowerCase())) {
            newResult.push(splits[i]);
          }
        }
      }
      
      if (newResult.length > 1) {
        result = newResult;
        break;
      }
    }
    
    return result;
  }

  // コマンド部分を解析
  private parseCommand(commandText: string): SlashTalkCommand {
    const normalizedCommand = commandText.toLowerCase();
    
    // アクションキーワードを検索
    for (const [action, keywords] of Object.entries(this.actionKeywords)) {
      for (const keyword of keywords) {
        if (normalizedCommand.includes(keyword)) {
          return {
            type: 'action',
            content: action,
            originalText: commandText
          };
        }
      }
    }

    // 特定のアクションが見つからない場合は汎用コマンドとして扱う
    return {
      type: 'command',
      content: commandText,
      originalText: commandText
    };
  }

  // 処理済みテキストを生成
  private generateProcessedText(commands: SlashTalkCommand[]): string {
    const inputCommands = commands.filter(cmd => cmd.type === 'input');
    const actionCommands = commands.filter(cmd => cmd.type === 'action');
    const otherCommands = commands.filter(cmd => cmd.type === 'command');

    let result = '';

    // 入力テキスト部分
    if (inputCommands.length > 0) {
      result = inputCommands.map(cmd => cmd.content).join(' ');
    }

    // アクションコマンドがある場合は実行指示を追加
    if (actionCommands.length > 0) {
      const actions = actionCommands.map(cmd => cmd.content).join(', ');
      result += result ? ` [実行: ${actions}]` : `[実行: ${actions}]`;
    }

    // その他のコマンドがある場合は追加
    if (otherCommands.length > 0) {
      const others = otherCommands.map(cmd => cmd.content).join(', ');
      result += result ? ` [コマンド: ${others}]` : `[コマンド: ${others}]`;
    }

    return result;
  }

  // コマンドを実行
  executeCommand(command: SlashTalkCommand, context?: any): boolean {
    console.log(`🎤 スラッシュトーク実行:`, command);

    switch (command.type) {
      case 'action':
        return this.executeAction(command.content, context);
      case 'command':
        return this.executeCustomCommand(command.content, context);
      default:
        return false;
    }
  }

  // アクションを実行
  private executeAction(action: string, context?: any): boolean {
    switch (action) {
      case 'send':
        if (context?.sendMessage) {
          context.sendMessage();
          return true;
        }
        break;
      case 'clear':
        if (context?.clearMessage) {
          context.clearMessage();
          return true;
        }
        break;
      case 'copy':
        if (context?.copyToClipboard) {
          context.copyToClipboard();
          return true;
        }
        break;
      // 他のアクションも同様に実装
      default:
        console.warn(`未対応のアクション: ${action}`);
        return false;
    }
    return false;
  }

  // カスタムコマンドを実行
  private executeCustomCommand(command: string, context?: any): boolean {
    // カスタムコマンドの処理ロジック
    console.log(`カスタムコマンド実行: ${command}`);
    return true;
  }

  // 使用例とヘルプテキストを生成
  getHelpText(): string {
    return `
🎤 スラッシュトーク使用方法:

基本構文: "入力テキスト スラッシュ 指示"

例:
• "こんにちは スラッシュ 送信" → メッセージを送信
• "このコードを スラッシュ 説明して" → コード説明を要求
• "エラーログ スラッシュ 分析" → ログ分析を実行
• "設定ファイル スラッシュ フォーマット" → ファイル整形

対応アクション:
• 送信、実行、保存、コピー、ペースト
• 検索、置換、翻訳、要約、説明、分析
• 生成、編集、レビュー、確認

キーワード:
• スラッシュ、コマンド、指示 など
    `.trim();
  }
}

// グローバルインスタンス
export const slashTalkProcessor = new SlashTalkProcessor();
