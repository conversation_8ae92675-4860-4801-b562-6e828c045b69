// 拡張テンプレートシステム
// 業界・用途別の専門テンプレートを提供

export interface TemplateMetadata {
  id: string;
  name: string;
  category: string;
  description: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  requiredTools?: string[];
  outputFormat: 'markdown' | 'yaml' | 'json' | 'mermaid' | 'mixed';
}

export interface TemplateField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'date' | 'number' | 'boolean';
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
  helpText?: string;
}

export interface Template {
  metadata: TemplateMetadata;
  fields: TemplateField[];
  generateContent: (data: Record<string, any>) => string;
  relatedTemplates?: string[];
  examples?: Record<string, any>[];
}

// プレゼンテーション・資料作成テンプレート
export const presentationTemplates: Template[] = [
  {
    metadata: {
      id: 'business-presentation',
      name: 'ビジネスプレゼンテーション',
      category: 'プレゼンテーション',
      description: '企業向けビジネスプレゼンテーション資料',
      tags: ['ビジネス', 'プレゼン', '企画書'],
      difficulty: 'intermediate',
      estimatedTime: '2-3時間',
      requiredTools: ['Marp', 'PowerPoint'],
      outputFormat: 'markdown'
    },
    fields: [
      { id: 'title', label: 'プレゼンタイトル', type: 'text', required: true },
      { id: 'presenter', label: '発表者', type: 'text', required: true },
      { id: 'audience', label: '対象者', type: 'text', required: true },
      { id: 'objective', label: 'プレゼン目的', type: 'textarea', required: true },
      { id: 'duration', label: '発表時間（分）', type: 'number', required: true },
      { id: 'sections', label: '主要セクション', type: 'textarea', required: true, helpText: '各セクションを改行で区切って入力' },
      { id: 'callToAction', label: 'コールトゥアクション', type: 'text', required: true },
      { id: 'theme', label: 'テーマカラー', type: 'select', required: false, options: ['blue', 'green', 'red', 'purple', 'orange'] }
    ],
    generateContent: (data) => `---
marp: true
theme: ${data.theme || 'default'}
paginate: true
---

# ${data.title}

**発表者**: ${data.presenter}
**対象者**: ${data.audience}
**発表時間**: ${data.duration}分

---

## アジェンダ

${data.sections.split('\n').map((section: string, index: number) => `${index + 1}. ${section.trim()}`).join('\n')}

---

## プレゼン目的

${data.objective}

---

${data.sections.split('\n').map((section: string) => `
## ${section.trim()}

<!-- ここに${section.trim()}の内容を記載 -->

- ポイント1
- ポイント2
- ポイント3

---
`).join('')}

## まとめ・次のアクション

### ${data.callToAction}

- 具体的なアクション1
- 具体的なアクション2
- 具体的なアクション3

---

## ご質問・ディスカッション

ありがとうございました

**連絡先**: [メールアドレス]
`,
    relatedTemplates: ['marp-slides', 'pitch-deck'],
    examples: [{
      title: '新サービス提案',
      presenter: '田中太郎',
      audience: '経営陣',
      objective: '新サービスの承認を得る',
      duration: 30,
      sections: '市場分析\n競合調査\nサービス概要\n収益予測\n実装計画',
      callToAction: '新サービス開発の承認をお願いします',
      theme: 'blue'
    }]
  },

  {
    metadata: {
      id: 'marp-slides',
      name: 'Marpスライド',
      category: 'プレゼンテーション',
      description: 'Markdown形式のプレゼンテーションスライド',
      tags: ['Marp', 'Markdown', 'スライド'],
      difficulty: 'beginner',
      estimatedTime: '1-2時間',
      requiredTools: ['Marp'],
      outputFormat: 'markdown'
    },
    fields: [
      { id: 'title', label: 'スライドタイトル', type: 'text', required: true },
      { id: 'author', label: '作成者', type: 'text', required: true },
      { id: 'theme', label: 'テーマ', type: 'select', required: true, options: ['default', 'gaia', 'uncover'] },
      { id: 'slides', label: 'スライド内容', type: 'textarea', required: true, helpText: '各スライドを"---"で区切って入力' }
    ],
    generateContent: (data) => `---
marp: true
theme: ${data.theme}
paginate: true
---

# ${data.title}

${data.author}

---

${data.slides.replace(/---/g, '\n---\n')}
`,
    relatedTemplates: ['business-presentation']
  }
];

// 士業系テンプレート
export const legalTemplates: Template[] = [
  {
    metadata: {
      id: 'legal-contract-review',
      name: '契約書レビューチェックリスト',
      category: '士業・法務',
      description: '契約書の法的リスクを体系的にチェック',
      tags: ['法務', '契約書', 'リスク管理'],
      difficulty: 'advanced',
      estimatedTime: '3-4時間',
      outputFormat: 'yaml'
    },
    fields: [
      { id: 'contractType', label: '契約種別', type: 'select', required: true, options: ['業務委託', '売買', 'ライセンス', '雇用', 'NDA'] },
      { id: 'parties', label: '契約当事者', type: 'textarea', required: true },
      { id: 'contractValue', label: '契約金額', type: 'text', required: false },
      { id: 'duration', label: '契約期間', type: 'text', required: true },
      { id: 'jurisdiction', label: '準拠法・管轄', type: 'text', required: true },
      { id: 'specialClauses', label: '特記事項', type: 'textarea', required: false }
    ],
    generateContent: (data) => `# 契約書レビューチェックリスト

## 基本情報
contract_type: "${data.contractType}"
parties: |
  ${data.parties}
contract_value: "${data.contractValue || 'N/A'}"
duration: "${data.duration}"
jurisdiction: "${data.jurisdiction}"

## チェック項目

### 1. 当事者・定義条項
- [ ] 当事者の法的地位・権限確認
- [ ] 用語定義の明確性
- [ ] 代表者・署名権限の確認

### 2. 契約の目的・範囲
- [ ] 契約目的の明確性
- [ ] 業務範囲・責任範囲の特定
- [ ] 成果物・納期の明確化

### 3. 対価・支払条件
- [ ] 対価の妥当性
- [ ] 支払条件・期限
- [ ] 遅延損害金の設定

### 4. 責任・リスク分担
- [ ] 損害賠償責任の範囲
- [ ] 免責条項の妥当性
- [ ] 保険・担保の要否

### 5. 知的財産権
- [ ] 知的財産権の帰属
- [ ] ライセンス条件
- [ ] 侵害時の対応

### 6. 秘密保持
- [ ] 秘密情報の定義
- [ ] 保持期間・返却義務
- [ ] 違反時の措置

### 7. 契約終了・解除
- [ ] 終了条件の明確化
- [ ] 解除事由の妥当性
- [ ] 終了後の処理

### 8. 紛争解決
- [ ] 準拠法の適切性
- [ ] 管轄裁判所の指定
- [ ] ADR条項の検討

## 特記事項
special_notes: |
  ${data.specialClauses || 'なし'}

## リスク評価
risk_level: "要評価"
recommended_actions:
  - 詳細な法的レビューの実施
  - 必要に応じた条項修正
  - 専門家への相談検討

## レビュー完了日
review_date: "${new Date().toISOString().split('T')[0]}"
reviewer: "要記入"
`
  },

  {
    metadata: {
      id: 'tax-planning-checklist',
      name: '税務プランニングチェックリスト',
      category: '士業・税務',
      description: '個人・法人の税務最適化チェック',
      tags: ['税務', '節税', 'プランニング'],
      difficulty: 'advanced',
      estimatedTime: '2-3時間',
      outputFormat: 'yaml'
    },
    fields: [
      { id: 'entityType', label: '事業形態', type: 'select', required: true, options: ['個人事業主', '法人', '合同会社', '株式会社'] },
      { id: 'businessType', label: '業種', type: 'text', required: true },
      { id: 'annualRevenue', label: '年間売上', type: 'text', required: true },
      { id: 'employees', label: '従業員数', type: 'number', required: false },
      { id: 'fiscalYear', label: '事業年度', type: 'text', required: true }
    ],
    generateContent: (data) => `# 税務プランニングチェックリスト

## 基本情報
entity_type: "${data.entityType}"
business_type: "${data.businessType}"
annual_revenue: "${data.annualRevenue}"
employees: ${data.employees || 0}
fiscal_year: "${data.fiscalYear}"

## 税務最適化チェック項目

### 1. 所得税・法人税対策
- [ ] 適切な事業形態の選択
- [ ] 役員報酬の最適化
- [ ] 退職金制度の活用
- [ ] 設備投資による節税

### 2. 消費税対策
- [ ] 課税事業者選択の検討
- [ ] 簡易課税制度の適用判定
- [ ] インボイス制度への対応
- [ ] 輸出免税の活用

### 3. 相続・事業承継対策
- [ ] 事業承継計画の策定
- [ ] 株式評価の最適化
- [ ] 生前贈与の活用
- [ ] 事業承継税制の適用

### 4. その他の税務対策
- [ ] ふるさと納税の活用
- [ ] iDeCo・企業型DCの活用
- [ ] 小規模企業共済の加入
- [ ] 経営セーフティ共済の活用

## 年間スケジュール
tax_calendar:
  - month: "3月"
    tasks: ["確定申告", "決算準備"]
  - month: "6月"
    tasks: ["住民税決定", "賞与支給"]
  - month: "12月"
    tasks: ["年末調整", "来年度計画"]

## 推奨アクション
recommendations:
  - 税理士との定期相談
  - 最新税制改正の確認
  - 節税シミュレーションの実施

planning_date: "${new Date().toISOString().split('T')[0]}"
`
  }
];

// ライター・コンテンツ制作テンプレート
export const contentTemplates: Template[] = [
  {
    metadata: {
      id: 'blog-article-structure',
      name: 'ブログ記事構成案',
      category: 'ライティング',
      description: 'SEOを意識したブログ記事の構成テンプレート',
      tags: ['ブログ', 'SEO', 'コンテンツ'],
      difficulty: 'intermediate',
      estimatedTime: '1-2時間',
      outputFormat: 'markdown'
    },
    fields: [
      { id: 'title', label: '記事タイトル', type: 'text', required: true },
      { id: 'targetKeyword', label: 'ターゲットキーワード', type: 'text', required: true },
      { id: 'targetAudience', label: 'ターゲット読者', type: 'text', required: true },
      { id: 'wordCount', label: '目標文字数', type: 'number', required: true },
      { id: 'purpose', label: '記事の目的', type: 'textarea', required: true },
      { id: 'mainPoints', label: '主要ポイント', type: 'textarea', required: true, helpText: '各ポイントを改行で区切って入力' }
    ],
    generateContent: (data) => `# ${data.title}

## 記事概要
- **ターゲットキーワード**: ${data.targetKeyword}
- **ターゲット読者**: ${data.targetAudience}
- **目標文字数**: ${data.wordCount}文字
- **記事の目的**: ${data.purpose}

## 記事構成

### 1. 導入部（10%）
- 読者の悩み・課題の提示
- 記事で解決できることの明示
- 読み進める理由の提供

### 2. 本文（80%）
${data.mainPoints.split('\n').map((point: string, index: number) => `
#### ${index + 1}. ${point.trim()}
- 詳細説明
- 具体例・事例
- 読者への行動提案
`).join('')}

### 3. まとめ（10%）
- 重要ポイントの再確認
- 読者への具体的なアクション提案
- 関連記事への誘導

## SEO対策チェックリスト
- [ ] タイトルにターゲットキーワードを含める
- [ ] 見出しタグ（H2, H3）を適切に使用
- [ ] 内部リンクを3-5個設置
- [ ] 画像にalt属性を設定
- [ ] メタディスクリプションを作成

## 執筆スケジュール
- **構成作成**: 1日目
- **執筆**: 2-3日目
- **校正・編集**: 4日目
- **公開**: 5日目

---
作成日: ${new Date().toLocaleDateString('ja-JP')}
`
  },

  {
    metadata: {
      id: 'podcast-script',
      name: 'ポッドキャスト台本',
      category: 'コンテンツ制作',
      description: 'ポッドキャスト番組の台本テンプレート',
      tags: ['ポッドキャスト', '音声', '台本'],
      difficulty: 'intermediate',
      estimatedTime: '2-3時間',
      outputFormat: 'markdown'
    },
    fields: [
      { id: 'episodeTitle', label: 'エピソードタイトル', type: 'text', required: true },
      { id: 'episodeNumber', label: 'エピソード番号', type: 'number', required: true },
      { id: 'hosts', label: 'ホスト', type: 'text', required: true },
      { id: 'guests', label: 'ゲスト', type: 'text', required: false },
      { id: 'duration', label: '予定時間（分）', type: 'number', required: true },
      { id: 'mainTopic', label: 'メイントピック', type: 'textarea', required: true },
      { id: 'segments', label: 'セグメント構成', type: 'textarea', required: true, helpText: '各セグメントを改行で区切って入力' }
    ],
    generateContent: (data) => `# ${data.episodeTitle}

## エピソード情報
- **エピソード**: #${data.episodeNumber}
- **ホスト**: ${data.hosts}
- **ゲスト**: ${data.guests || 'なし'}
- **予定時間**: ${data.duration}分
- **収録日**: ${new Date().toLocaleDateString('ja-JP')}

## 番組概要
${data.mainTopic}

## 台本構成

### オープニング（2-3分）
**ホスト**: 
> こんにちは、[番組名]の${data.hosts}です。今日は第${data.episodeNumber}回目の配信です。

**今回のトピック紹介**:
> 今日は「${data.episodeTitle}」というテーマでお話しします。

${data.guests ? `**ゲスト紹介**:\n> 今日は特別ゲストとして、${data.guests}さんにお越しいただきました。` : ''}

### メインコンテンツ（${data.duration - 5}分）
${data.segments.split('\n').map((segment: string, index: number) => `
#### セグメント${index + 1}: ${segment.trim()}（約${Math.floor((data.duration - 5) / data.segments.split('\n').length)}分）

**話すポイント**:
- ポイント1
- ポイント2
- ポイント3

**想定される質問・コメント**:
- Q: [想定質問]
- A: [回答案]
`).join('')}

### エンディング（2分）
**まとめ**:
> 今日は${data.episodeTitle}についてお話ししました。

**次回予告**:
> 次回は[次回のテーマ]についてお話しする予定です。

**お知らせ・連絡先**:
> 番組への感想やリクエストは[連絡先]までお寄せください。

**クロージング**:
> それでは、また次回お会いしましょう。${data.hosts}でした。

## 収録メモ
- [ ] 音声レベルチェック
- [ ] BGM・効果音の準備
- [ ] 資料・参考リンクの準備
- [ ] 収録環境の確認

## 編集ポイント
- [ ] 不要な間の削除
- [ ] 音量レベルの調整
- [ ] BGM・ジングルの挿入
- [ ] チャプター分けの設定

---
台本作成日: ${new Date().toLocaleDateString('ja-JP')}
`
  }
];

// テンプレート管理クラス
export class EnhancedTemplateManager {
  private templates: Map<string, Template> = new Map();

  constructor() {
    this.loadTemplates();
  }

  private loadTemplates() {
    [...presentationTemplates, ...legalTemplates, ...contentTemplates].forEach(template => {
      this.templates.set(template.metadata.id, template);
    });
  }

  getTemplate(id: string): Template | undefined {
    return this.templates.get(id);
  }

  getTemplatesByCategory(category: string): Template[] {
    return Array.from(this.templates.values()).filter(
      template => template.metadata.category === category
    );
  }

  getTemplatesByTag(tag: string): Template[] {
    return Array.from(this.templates.values()).filter(
      template => template.metadata.tags.includes(tag)
    );
  }

  getAllCategories(): string[] {
    const categories = new Set<string>();
    this.templates.forEach(template => {
      categories.add(template.metadata.category);
    });
    return Array.from(categories);
  }

  searchTemplates(query: string): Template[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.templates.values()).filter(template => 
      template.metadata.name.toLowerCase().includes(lowerQuery) ||
      template.metadata.description.toLowerCase().includes(lowerQuery) ||
      template.metadata.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  generateContent(templateId: string, data: Record<string, any>): string {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }
    return template.generateContent(data);
  }
}

// グローバルインスタンス
export const enhancedTemplateManager = new EnhancedTemplateManager();
