'use client';

import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';

interface CharacterModel {
  id: string;
  name: string;
  type: 'VRM' | 'FBX' | 'LIVE2D' | 'GLB';
  url?: string; // HTTP URLの場合のみ使用
  arrayBuffer?: ArrayBuffer; // ファイルデータを直接保存
  isLoaded: boolean;
  defaultSettings?: {
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    scale?: { x: number; y: number; z: number };
    emotion?: 'neutral' | 'happy' | 'thinking' | 'speaking' | 'listening';
    lipSyncEnabled?: boolean;
    animationLoop?: string;
  };
}

interface AgentVRMSettings {
  model: CharacterModel | null;
  emotion: 'neutral' | 'happy' | 'thinking' | 'speaking' | 'listening';
  lastMessage?: string;
  isVisible: boolean;
  customSettings?: CharacterModel['defaultSettings'];
}

interface CharacterState {
  // エージョント個別VRM設定
  agentVRMSettings: { [agentId: string]: AgentVRMSettings };
  currentAgentId: string | null;
  globalSettings: {
    isMuted: boolean;
    isActive: boolean;
  };
}

interface CharacterContextValue {
  characterState: CharacterState;
  // エージェント管理
  setCurrentAgent: (agentId: string) => void;
  getCurrentAgentVRM: () => CharacterModel | null;
  setAgentVRMModel: (agentId: string, model: CharacterModel | null) => void;
  setAgentVisibility: (agentId: string, visible: boolean) => void;
  setAgentEmotion: (agentId: string, emotion: AgentVRMSettings['emotion']) => void;
  updateAgentMessage: (agentId: string, message: string) => void;
  // グローバル設定
  setMuted: (muted: boolean) => void;
  setActive: (active: boolean) => void;
  // VRM操作
  uploadVRMModelForAgent: (agentId: string, file: File) => void;
  deleteAgentVRMModel: (agentId: string) => void;
  playAgentAnimation: (agentId: string, animationType: 'speak' | 'listen' | 'think' | 'idle' | 'reset') => void;
  // 設定永続化
  saveAgentSettings: (agentId: string) => void;
  loadAgentSettings: (agentId: string) => void;
  // ユーティリティ
  getAllAgentIds: () => string[];
  getAgentDisplayName: (agentId: string) => string;
}

const CharacterContext = createContext<CharacterContextValue | undefined>(undefined);

// エージェント定義マップ
const AGENT_DEFINITIONS = {
  'mother-cto': { name: '母', role: 'CTO', emoji: '👩‍💼' },
  'god-ceo': { name: '神', role: 'CEO', emoji: '👑' },
  'king-coo': { name: '王', role: 'COO', emoji: '👑' },
  'general-manager': { name: '将', role: 'Manager', emoji: '⚔️' },
  'soldier-worker': { name: '兵', role: 'Worker', emoji: '🛡️' }
};

export function CharacterProvider({ children }: { children: ReactNode }) {
  // ArrayBufferを別途メモリ内で管理（永続化対応）
  const [vrmArrayBuffers, setVrmArrayBuffers] = useState<Record<string, ArrayBuffer>>(() => {
    // 初期化時にlocalStorageからVRMデータを復元
    if (typeof window !== 'undefined') {
      try {
        const savedVRMData = localStorage.getItem('meta-studio-vrm-data');
        if (savedVRMData) {
          const parsed = JSON.parse(savedVRMData);
          const restoredBuffers: Record<string, ArrayBuffer> = {};

          Object.keys(parsed).forEach(agentId => {
            try {
              // Base64からArrayBufferに復元
              const base64Data = parsed[agentId];
              const binaryString = atob(base64Data);
              const bytes = new Uint8Array(binaryString.length);
              for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }
              restoredBuffers[agentId] = bytes.buffer;
              console.log(`🔄 VRMデータ復元: ${agentId} (${bytes.buffer.byteLength} bytes)`);
            } catch (error) {
              console.warn(`⚠️ VRMデータ復元失敗: ${agentId}`, error);
            }
          });

          return restoredBuffers;
        }
      } catch (error) {
        console.error('VRMデータ復元エラー:', error);
      }
    }
    return {};
  });

  const [characterState, setCharacterState] = useState<CharacterState>(() => {
    // localStorageから設定を復元
    const saved = typeof window !== 'undefined' ? localStorage.getItem('meta-studio-agent-vrm-settings') : null;
    const savedSettings = saved ? JSON.parse(saved) : {};

    return {
      agentVRMSettings: savedSettings,
      currentAgentId: 'mother-cto', // デフォルトは母
      globalSettings: {
        isMuted: false,
        isActive: false
      }
    };
  });

  // VRMデータ永続化関数（ArrayBufferをBase64でlocalStorageに保存）
  const saveVRMDataToStorage = (agentId: string, arrayBuffer: ArrayBuffer) => {
    if (typeof window !== 'undefined') {
      try {
        // ArrayBufferをBase64に変換
        const bytes = new Uint8Array(arrayBuffer);
        let binaryString = '';
        for (let i = 0; i < bytes.byteLength; i++) {
          binaryString += String.fromCharCode(bytes[i]);
        }
        const base64Data = btoa(binaryString);

        // 既存のVRMデータを取得
        const existingData = localStorage.getItem('meta-studio-vrm-data');
        const vrmData = existingData ? JSON.parse(existingData) : {};

        // 新しいデータを追加
        vrmData[agentId] = base64Data;

        localStorage.setItem('meta-studio-vrm-data', JSON.stringify(vrmData));
        console.log(`💾 VRMデータ永続化完了: ${agentId} (${arrayBuffer.byteLength} bytes → Base64)`);
      } catch (error) {
        console.error(`VRMデータ永続化エラー: ${agentId}`, error);
      }
    }
  };

  // VRMデータ削除関数
  const removeVRMDataFromStorage = (agentId: string) => {
    if (typeof window !== 'undefined') {
      try {
        const existingData = localStorage.getItem('meta-studio-vrm-data');
        if (existingData) {
          const vrmData = JSON.parse(existingData);
          delete vrmData[agentId];
          localStorage.setItem('meta-studio-vrm-data', JSON.stringify(vrmData));
          console.log(`🗑️ VRMデータ削除完了: ${agentId}`);
        }
      } catch (error) {
        console.error(`VRMデータ削除エラー: ${agentId}`, error);
      }
    }
  };

  // 設定永続化関数（ArrayBufferを除外してlocalStorageに保存）
  const saveToLocalStorage = (state: CharacterState) => {
    if (typeof window !== 'undefined') {
      try {
        // ArrayBufferを除外した設定のみをlocalStorageに保存
        const serializableSettings: any = {};
        Object.keys(state.agentVRMSettings).forEach(agentId => {
          const setting = state.agentVRMSettings[agentId];
          serializableSettings[agentId] = {
            emotion: setting.emotion,
            isVisible: setting.isVisible,
            // modelのメタデータのみ保存（ArrayBufferは除外）
            modelInfo: setting.model ? {
              id: setting.model.id,
              name: setting.model.name,
              type: setting.model.type,
              isLoaded: setting.model.isLoaded
            } : null
          };
        });

        localStorage.setItem('meta-studio-agent-vrm-settings', JSON.stringify(serializableSettings));
        console.log('💾 VRM設定をlocalStorageに保存（ArrayBuffer除外）');
      } catch (error) {
        console.error('❌ localStorage保存エラー:', error);
      }
    }
  };

  // エージェント関連関数（useCallbackでメモ化）
  const setCurrentAgent = useCallback((agentId: string) => {
    setCharacterState(prev => ({ ...prev, currentAgentId: agentId }));
    console.log(`🔄 setCurrentAgent: ${agentId}`);
  }, []);

  const getCurrentAgentVRM = useCallback((): CharacterModel | null => {
    const currentId = characterState.currentAgentId;
    const model = currentId ? characterState.agentVRMSettings[currentId]?.model || null : null;

    // ArrayBufferをメモリから復元
    if (model && currentId && vrmArrayBuffers[currentId]) {
      model.arrayBuffer = vrmArrayBuffers[currentId];
    }

    console.log(`🔍 getCurrentAgentVRM: ${currentId} → ${model?.name || 'なし'}`);
    console.log(`📊 ArrayBuffer状況:`, {
      hasArrayBuffer: !!model?.arrayBuffer,
      arrayBufferSize: model?.arrayBuffer?.byteLength || 0,
      memoryBuffers: Object.keys(vrmArrayBuffers).length
    });
    console.log(`📊 全エージェントVRM状況:`, Object.keys(characterState.agentVRMSettings).map(id => ({
      id,
      hasModel: !!characterState.agentVRMSettings[id]?.model,
      modelName: characterState.agentVRMSettings[id]?.model?.name,
      hasArrayBuffer: !!vrmArrayBuffers[id]
    })));

    return model;
  }, [characterState.currentAgentId, characterState.agentVRMSettings, vrmArrayBuffers]);

  // 重複定義削除 - 正しいsaveAgentSettingsは233行目付近にあり

  const setAgentVisibility = (agentId: string, visible: boolean) => {
    setCharacterState(prev => ({
      ...prev,
      agentVRMSettings: {
        ...prev.agentVRMSettings,
        [agentId]: {
          ...prev.agentVRMSettings[agentId],
          isVisible: visible
        }
      }
    }));
  };

  const setAgentEmotion = (agentId: string, emotion: AgentVRMSettings['emotion']) => {
    setCharacterState(prev => ({
      ...prev,
      agentVRMSettings: {
        ...prev.agentVRMSettings,
        [agentId]: {
          ...prev.agentVRMSettings[agentId],
          emotion
        }
      }
    }));
  };

  const updateAgentMessage = (agentId: string, message: string) => {
    setCharacterState(prev => ({
      ...prev,
      agentVRMSettings: {
        ...prev.agentVRMSettings,
        [agentId]: {
          ...prev.agentVRMSettings[agentId],
          lastMessage: message
        }
      }
    }));
  };

  // グローバル設定
  const setMuted = (muted: boolean) => {
    setCharacterState(prev => ({
      ...prev,
      globalSettings: { ...prev.globalSettings, isMuted: muted }
    }));
  };

  const setActive = (active: boolean) => {
    setCharacterState(prev => ({
      ...prev,
      globalSettings: { ...prev.globalSettings, isActive: active }
    }));
  };

  // 旧いplayAnimation関数は使用せず、playAgentAnimationを使用

  // VRM操作関数（ArrayBuffer方式）
  const uploadVRMModelForAgent = async (agentId: string, file: File) => {
    console.log(`🎨 ${agentId}にVRMモデルをアップロード開始:`, file.name);
    
    try {
      // ファイルをArrayBufferとして読み込み
      const arrayBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          if (reader.result instanceof ArrayBuffer) {
            console.log(`📄 ファイル読み込み完了: ${reader.result.byteLength} bytes`);
            resolve(reader.result);
          } else {
            reject(new Error('ArrayBuffer読み込み失敗'));
          }
        };
        reader.onerror = () => reject(new Error('ファイル読み込みエラー'));
        reader.readAsArrayBuffer(file);
      });
      
      const model: CharacterModel = {
        id: `${agentId}-${Date.now()}`,
        name: file.name,
        type: 'VRM',
        arrayBuffer, // ArrayBufferを直接保存
        isLoaded: false
      };

      console.log(`📦 作成したVRMモデル:`, model.id, model.name, `${arrayBuffer.byteLength} bytes`);

      // ArrayBufferを別途メモリに保存
      setVrmArrayBuffers(prev => ({
        ...prev,
        [agentId]: arrayBuffer
      }));

      // VRMデータを永続化
      saveVRMDataToStorage(agentId, arrayBuffer);

      setAgentVRMModel(agentId, model);
      console.log(`✅ ${agentId}にVRMモデルをアップロード完了:`, file.name, `(${arrayBuffer.byteLength} bytes)`);
      console.log(`💾 ArrayBufferをメモリ&永続化保存:`, agentId, `${arrayBuffer.byteLength} bytes`);
    } catch (error) {
      console.error(`❌ ${agentId}のVRMアップロード失敗:`, error);
      throw error;
    }
  };

  const deleteAgentVRMModel = (agentId: string) => {
    // ArrayBufferもメモリから削除
    setVrmArrayBuffers(prev => {
      const newBuffers = { ...prev };
      delete newBuffers[agentId];
      return newBuffers;
    });

    // 永続化データも削除
    removeVRMDataFromStorage(agentId);

    setAgentVRMModel(agentId, null);
    console.log(`🗑️ ${agentId}のVRMモデルを削除（ArrayBuffer&永続化データも削除）`);
  };

  const playAgentAnimation = (agentId: string, animationType: 'speak' | 'listen' | 'think' | 'idle' | 'reset') => {
    console.log(`🎦 ${agentId}アニメーション: ${animationType}`);
    
    switch (animationType) {
      case 'speak':
        setAgentEmotion(agentId, 'speaking');
        setActive(true);
        break;
      case 'listen':
        setAgentEmotion(agentId, 'listening');
        setActive(true);
        break;
      case 'think':
        setAgentEmotion(agentId, 'thinking');
        setActive(true);
        break;
      case 'idle':
        setAgentEmotion(agentId, 'neutral');
        setActive(false);
        break;
      case 'reset':
        setAgentEmotion(agentId, 'neutral');
        setActive(false);
        console.log(`🔄 ${agentId}VRMポーズとアニメーションをリセット`);
        break;
    }

    // 一定時間後にアイドル状態に戻る
    if (animationType !== 'idle' && animationType !== 'reset') {
      setTimeout(() => {
        setAgentEmotion(agentId, 'neutral');
        setActive(false);
      }, 3000);
    }
  };

  // setAgentVRMModel関数を実装
  const setAgentVRMModel = useCallback((agentId: string, model: CharacterModel | null) => {
    console.log(`🔧 setAgentVRMModel呼び出し: ${agentId} →`, model?.name || 'null');
    
    setCharacterState(prev => {
      console.log(`📋 現在の状態:`, prev.agentVRMSettings[agentId]);
      
      const newState = {
        ...prev,
        agentVRMSettings: {
          ...prev.agentVRMSettings,
          [agentId]: {
            model,
            emotion: prev.agentVRMSettings[agentId]?.emotion || 'neutral',
            isVisible: prev.agentVRMSettings[agentId]?.isVisible ?? true
          }
        }
      };
      
      console.log(`📋 新しい状態:`, newState.agentVRMSettings[agentId]);
      
      // 即座にlocalStorageに保存
      saveToLocalStorage(newState);
      console.log(`🎨 ${agentId}にVRMモデル設定:`, model?.name || 'null');
      
      return newState;
    });
  }, []);

  const loadAgentSettings = useCallback((agentId: string) => {
    // この関数は初期化時に必要な設定を作成
    setCharacterState(prev => {
      if (!prev.agentVRMSettings[agentId]) {
        console.log(`📂 ${agentId}のデフォルト設定作成`);
        return {
          ...prev,
          agentVRMSettings: {
            ...prev.agentVRMSettings,
            [agentId]: {
              model: null,
              emotion: 'neutral',
              isVisible: true
            }
          }
        };
      }
      return prev;
    });
  }, []);

  // ユーティリティ関数
  const getAllAgentIds = (): string[] => {
    return Object.keys(AGENT_DEFINITIONS);
  };

  const getAgentDisplayName = (agentId: string): string => {
    return AGENT_DEFINITIONS[agentId as keyof typeof AGENT_DEFINITIONS]?.name || agentId;
  };

  // 設定永続化関数
  const saveAgentSettings = useCallback((agentId: string) => {
    setCharacterState(prev => {
      saveToLocalStorage(prev);
      console.log(`💾 ${agentId}の設定を保存`);
      return prev;
    });
  }, []);

  return (
    <CharacterContext.Provider
      value={{
        characterState,
        // エージェント管理
        setCurrentAgent,
        getCurrentAgentVRM,
        setAgentVRMModel,
        setAgentVisibility,
        setAgentEmotion,
        updateAgentMessage,
        // グローバル設定
        setMuted,
        setActive,
        // VRM操作
        uploadVRMModelForAgent,
        deleteAgentVRMModel,
        playAgentAnimation,
        // 設定永続化
        saveAgentSettings,
        loadAgentSettings,
        // ユーティリティ
        getAllAgentIds,
        getAgentDisplayName
      }}
    >
      {children}
    </CharacterContext.Provider>
  );
}

export function useCharacter() {
  const context = useContext(CharacterContext);
  if (context === undefined) {
    throw new Error('useCharacter must be used within a CharacterProvider');
  }
  return context;
}