'use client';

import { ReactNode, useState, useEffect } from 'react';
import { Rocket } from 'lucide-react';
import Sidebar from './Sidebar';
import ISSystem from './ISSystem';
import MetaTerminal from './MetaTerminal';
import Launcher from './Launcher';
import XtilesDashboard from './XtilesDashboard';
import DashboardGrid from './DashboardGrid';
import TabManager from './TabManager';
import ProjectView from './ProjectView';
import LauncherView from './LauncherView';
import ExpoQRView from './ExpoQRView';
import Editor from './Editor';
import ResizablePanel from './ResizablePanel';
import SettingsPanel from './SettingsPanel';
import SystemPlaceholder from './SystemPlaceholder';
import NoteManager from './NoteManager';
import StatsDashboard from './StatsDashboard';
import PluginStore from './PluginStore';
import AgentDashboard from './AgentDashboard';
import VoiceInput from './VoiceInput';
import BrowserAutomationPanel from './BrowserAutomationPanel';
// import MultiCharacterStudio from './MultiCharacterStudio';
import InboxSystem from './InboxSystem';
import SystemOverview from './SystemOverview';
import FileExplorer from './FileExplorer';
import MetaStudioDashboard from './MetaStudioDashboard';
import FileViewer from './FileViewer';
import TaskManager from './TaskManager';
// VRMDebugPanelを無効化（競合回避のため）
// import VRMDebugPanel from './VRMDebugPanel';
import { CharacterProvider } from '../contexts/CharacterContext';

interface MetaStudioLayoutProps {
  children: ReactNode;
}

interface CreatedProject {
  name: string;
  template: any;
}

export default function MetaStudioLayout({ children }: MetaStudioLayoutProps) {
  const [isLauncherOpen, setIsLauncherOpen] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [activeTab, setActiveTab] = useState<any>(null);
  const [currentView, setCurrentView] = useState<React.ReactNode>(<DashboardGrid />);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [projectListVersion, setProjectListVersion] = useState(0);
  const [lastCreatedProject, setLastCreatedProject] = useState<CreatedProject | null>(null);
  const [projectNameUpdate, setProjectNameUpdate] = useState<{oldName: string, newName: string, timestamp: number} | null>(null);
  const [isDebugPanelVisible, setIsDebugPanelVisible] = useState(false);
  
  // ターミナル位置・高さをグローバル状態として管理（Hydration問題解決）
  const [terminalHeight, setTerminalHeight] = useState(350); // デフォルト値で初期化
  const [isClient, setIsClient] = useState(false);
  
  // ターミナル高さをlocalStorageに保存
  const saveTerminalHeight = (height: number) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('meta-studio-terminal-height', height.toString());
      setTerminalHeight(height);
    }
  };
  
  // サイドバー幅をグローバル状態として管理（Hydrationエラー完全回避）
  // サーバーサイドとクライアントサイドで同じ初期値を使用
  const [leftSidebarWidth, setLeftSidebarWidth] = useState(320); // 見やすいサイズに最適化
  const [rightSidebarWidth, setRightSidebarWidth] = useState(350); // 固定初期値

  // クライアントサイド初期化（Hydration後）
  useEffect(() => {
    // 最適なレイアウトサイズをlocalStorageに保存
    if (typeof window !== 'undefined') {
      const savedLeftWidth = localStorage.getItem('meta-studio-optimal-left-width');
      if (!savedLeftWidth) {
        localStorage.setItem('meta-studio-optimal-left-width', '320');
      }
      const savedRightWidth = localStorage.getItem('meta-studio-optimal-right-width');
      if (!savedRightWidth) {
        localStorage.setItem('meta-studio-optimal-right-width', '350');
      }
    }
  }, []);

  useEffect(() => {
    setIsClient(true);
    
    // ターミナル高さを復元
    const stored = localStorage.getItem('meta-studio-terminal-height');
    if (stored) {
      const height = parseInt(stored, 10);
      if (height >= 150 && height <= window.innerHeight * 0.8) {
        setTerminalHeight(height);
      }
    }
  }, []);

  // レスポンシブ対応・サイドバー幅自動調整（Hydration安全）
  useEffect(() => {
    const checkResponsive = () => {
      const screenWidth = window.innerWidth;
      const mobile = screenWidth < 768;
      setIsMobile(mobile);
      
      if (mobile) {
        setSidebarCollapsed(true);
      } else {
        // localStorageから保存された値を復元（Hydrationエラー回避）
        const storedLeft = localStorage.getItem('meta-studio-left-sidebar-width');
        const storedRight = localStorage.getItem('meta-studio-right-sidebar-width');

        if (storedLeft) {
          const parsedLeft = parseInt(storedLeft, 10);
          if (!isNaN(parsedLeft) && parsedLeft >= 240 && parsedLeft <= 400) {
            setLeftSidebarWidth(parsedLeft);
          }
        } else if (screenWidth >= 1400) {
          // 広画面では左サイドバーを少し広く
          setLeftSidebarWidth(320);
        }

        if (storedRight) {
          const parsedRight = parseInt(storedRight, 10);
          if (!isNaN(parsedRight) && parsedRight >= 350 && parsedRight <= 600) {
            setRightSidebarWidth(parsedRight);
          }
        } else {
          // 画面サイズに応じたデフォルト値
          if (screenWidth >= 1600) {
            setRightSidebarWidth(480); // 超大画面
          } else if (screenWidth >= 1400) {
            setRightSidebarWidth(450); // 大画面
          } else if (screenWidth >= 1200) {
            setRightSidebarWidth(420); // 中画面
          }
        }
      }
    };
    
    // 初回のみ実行（Hydration完了後）
    checkResponsive();
    
    // 改善されたリサイズイベント処理
    const handleResize = () => {
      const screenWidth = window.innerWidth;
      const mobile = screenWidth < 768;
      setIsMobile(mobile);
      
      if (mobile) {
        setSidebarCollapsed(true);
      } else {
        // デスクトップ時：画面幅に応じてサイドバー幅を動的調整（見切れ防止）
        const minLeftWidth = sidebarCollapsed ? 64 : 250;
        const maxLeftWidth = Math.min(400, Math.max(300, screenWidth * 0.25));
        const minRightWidth = 350;
        // 右サイドバーの最大幅を調整：画面が狭い場合は最小幅を優先
        const maxRightWidth = screenWidth >= 1200
          ? Math.min(600, screenWidth * 0.3)
          : Math.max(350, Math.min(450, screenWidth * 0.35));
        
        // 左サイドバー幅の調整
        setLeftSidebarWidth(prev => {
          const adjusted = Math.max(minLeftWidth, Math.min(maxLeftWidth, prev));
          if (adjusted !== prev) {
            localStorage.setItem('meta-studio-left-sidebar-width', adjusted.toString());
          }
          return adjusted;
        });
        
        // 右サイドバー幅の調整
        setRightSidebarWidth(prev => {
          const adjusted = Math.max(minRightWidth, Math.min(maxRightWidth, prev));
          if (adjusted !== prev) {
            localStorage.setItem('meta-studio-right-sidebar-width', adjusted.toString());
          }
          return adjusted;
        });
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [sidebarCollapsed]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsLauncherOpen(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // プロジェクト作成ハンドラ
  const handleCreateProject = (template: any, projectName: string) => {
    console.log('Creating project:', projectName, template);
    
    // テンプレートカテゴリからプロジェクトタイプを推測
    const getProjectType = (template: any): 'mobile' | 'web' | 'ai' | 'game' | 'desktop' => {
      const category = template?.category?.toLowerCase() || '';
      const name = template?.name?.toLowerCase() || '';
      const tags = template?.tags?.join(' ').toLowerCase() || '';
      const allText = `${category} ${name} ${tags}`;
      
      if (allText.includes('モバイル') || allText.includes('mobile') || allText.includes('アプリ') || allText.includes('react native') || allText.includes('flutter')) {
        return 'mobile';
      } else if (allText.includes('ai') || allText.includes('機械学習') || allText.includes('bot') || allText.includes('チャット') || allText.includes('llm')) {
        return 'ai';
      } else if (allText.includes('ゲーム') || allText.includes('game') || allText.includes('unity') || allText.includes('unreal')) {
        return 'game';
      } else if (allText.includes('デスクトップ') || allText.includes('desktop') || allText.includes('electron') || allText.includes('tauri')) {
        return 'desktop';
      } else {
        return 'web'; // デフォルト
      }
    };
    
    // 作成されたプロジェクト情報を保存（type情報を含む）
    const formattedProjectName = `${projectName}_projext`;
    const projectType = getProjectType(template);
    const enhancedTemplate = { ...template, type: projectType };
    
    setLastCreatedProject({ name: formattedProjectName, template: enhancedTemplate });
    
    // プロジェクトリストの更新をトリガー
    setProjectListVersion(prev => prev + 1);
    
    // プロジェクトビューを開く
    const newTab = { 
      id: `project-${formattedProjectName}`, 
      title: formattedProjectName, 
      type: 'project' as const,
      content: formattedProjectName 
    };
    
    console.log('Setting new tab:', newTab);
    setActiveTab(newTab);
    setCurrentView(<ProjectView 
      projectName={formattedProjectName} 
      onProjectNameChange={(newName) => {
        // タブタイトルを更新
        setActiveTab(prev => ({
          ...prev,
          title: newName,
          content: newName
        }));
        // プロジェクトリストを更新（サイドバーに反映）
        setProjectListVersion(prev => prev + 1);
      }}
    />);
    
    // ランチャーを閉じる
    setIsLauncherOpen(false);
  };

  return (
    <CharacterProvider>
      <div className="h-screen flex bg-base-100 overflow-hidden min-w-[1024px]">
      {/* 左サイドバー（完全固定位置・一貫性保証） */}
      {!isMobile && (
        <ResizablePanel 
          direction="horizontal" 
          initialSize={leftSidebarWidth} 
          minSize={sidebarCollapsed ? 64 : 240} 
          maxSize={400}
          className="bg-base-200 shadow-lg border-r border-base-content/10"
          storageKey="meta-studio-left-sidebar"
        >
        <Sidebar 
          onDashboardClick={() => {
            setActiveTab({ id: 'dashboard', title: 'ダッシュボード', type: 'dashboard' });
            setCurrentView(<XtilesDashboard />);
          }}
          onHomeClick={() => {
            setActiveTab({ id: 'home', title: 'ダッシュボード', type: 'home', isPinned: true });
            setCurrentView(<DashboardGrid />);
          }}
          onSettingsClick={() => {
            setActiveTab({ id: 'settings', title: '設定', type: 'system' });
            setCurrentView(<SettingsPanel />);
          }}
          onDebugClick={() => setIsDebugPanelVisible(!isDebugPanelVisible)}
          onProjectClick={(projectName) => {
            console.log('MetaStudioLayout: onProjectClick called with:', projectName);
            console.log('MetaStudioLayout: Current activeTab before:', activeTab);
            console.log('MetaStudioLayout: Current currentView before:', currentView);
            
            const newTab = { 
              id: `project-${projectName}`, 
              title: projectName, 
              type: 'project' as const,
              content: projectName 
            };
            console.log('MetaStudioLayout: Creating new tab:', newTab);
            
            // ProjectViewコンポーネントを作成
            const projectView = <ProjectView 
              projectName={projectName}
              onProjectNameChange={(newName) => {
                console.log('MetaStudioLayout: Project name change requested:', { oldName: projectName, newName });
                // タブタイトルを更新
                setActiveTab(prev => prev ? {
                  ...prev,
                  title: newName,
                  content: newName
                } : null);
                // プロジェクトリストを更新（サイドバーに反映）
                setProjectListVersion(prev => prev + 1);
                setProjectNameUpdate({ oldName: projectName, newName, timestamp: Date.now() });
              }}
            />;
            
            console.log('MetaStudioLayout: Setting currentView to ProjectView');
            setCurrentView(projectView);
            
            console.log('MetaStudioLayout: Setting activeTab');
            setActiveTab(newTab);
            
            console.log('MetaStudioLayout: FINAL - activeTab:', newTab);
            console.log('MetaStudioLayout: FINAL - currentView type:', projectView.type);
            
            // 少し遅延させてTabManagerに確実に反映
            setTimeout(() => {
              console.log('MetaStudioLayout: Delayed check - activeTab state:', activeTab);
              console.log('MetaStudioLayout: Delayed check - currentView state exists:', !!currentView);
            }, 100);
          }}
          onProjectNameUpdate={(oldName, newName) => {
            console.log('MetaStudioLayout: onProjectNameUpdate called', { oldName, newName });
            // プロジェクト名の更新をSidebarに反映する処理は、SidebarコンポーネントのupdateProjectName関数で処理される
          }}
          onLauncherClick={() => {
            setActiveTab({ id: 'launcher', title: 'アプリドック', type: 'launcher' });
            setCurrentView(<LauncherView 
              onProjectClick={(projectId, projectTitle) => {
                setActiveTab({ 
                  id: `project-${projectId}`, 
                  title: projectTitle, 
                  type: 'project',
                  content: projectId 
                });
                setCurrentView(<ProjectView 
                  projectName={projectTitle} 
                  onProjectNameChange={(newName) => {
                    // タブタイトルを更新
                    setActiveTab(prev => ({
                      ...prev,
                      title: newName,
                      content: newName
                    }));
                    // プロジェクトリストを更新（サイドバーに反映）
                    setProjectListVersion(prev => prev + 1);
                  }}
                />);
              }}
              onProjectCreate={handleCreateProject}
            />);
          }}
          projectListVersion={projectListVersion}
          lastCreatedProject={lastCreatedProject}
          projectNameUpdate={projectNameUpdate}
          onFileClick={(fileName, filePath) => {
            console.log('MetaStudioLayout: onFileClick called', { fileName, filePath });
            setActiveTab({ 
              id: `file-${fileName}`, 
              title: fileName, 
              type: 'file',
              content: filePath || fileName // フルパスを優先してcontentに設定
            });
            setCurrentView(<FileViewer fileName={fileName} filePath={filePath} />);
          }}
          onSystemClick={(system) => {
            let view = null;
            let title = system;
            
            switch (system) {
              case 'エディター':
                view = <Editor />;
                break;
              case 'インボックス':
                view = <InboxSystem />;
                break;
              case 'ファイルエクスプローラー':
                view = <FileExplorer />;
                title = 'ファイルエクスプローラー';
                break;
              case '管理':
                view = <MetaStudioDashboard />;
                title = '管理ダッシュボード';
                break;
              case '設定':
                view = <SettingsPanel />;
                title = '設定';
                break;
              case 'ブラウザ自動化':
                view = <BrowserAutomationPanel />;
                title = 'ブラウザ自動化';
                break;
              case 'エージェント管理':
                view = <AgentDashboard />;
                title = 'エージェント管理';
                break;
              case 'プラグイン':
                view = <PluginStore />;
                title = '拡張王ラグイン';
                break;
              case '統計サマリー':
                view = <StatsDashboard />;
                title = '統計サマリー';
                break;
              case 'Expo QR':
              case 'モバイル':
                view = <ExpoQRView />;
                title = 'モバイルプレビュー';
                break;
              case 'アプリドック':
                view = <LauncherView 
                  onProjectClick={(projectId, projectTitle) => {
                    setActiveTab({ 
                      id: `project-${projectId}`, 
                      title: projectTitle, 
                      type: 'project',
                      content: projectTitle 
                    });
                    setCurrentView(<ProjectView 
                      projectName={projectTitle} 
                      onProjectNameChange={(newName) => {
                        setActiveTab(prev => ({
                          ...prev,
                          title: newName,
                          content: newName
                        }));
                        setProjectListVersion(prev => prev + 1);
                      }}
                    />);
                  }}
                  onProjectCreate={handleCreateProject}
                />;
                title = 'アプリドック';
                break;
              case 'システム概要':
                view = (
                  <SystemOverview 
                    onLauncherClick={() => {
                      setActiveTab({ id: 'launcher', title: 'アプリドック', type: 'launcher' });
                      setCurrentView(<LauncherView 
                        onProjectClick={(projectId, projectTitle) => {
                          setActiveTab({ 
                            id: `project-${projectId}`, 
                            title: projectTitle, 
                            type: 'project',
                            content: projectTitle 
                          });
                          setCurrentView(<ProjectView 
                            projectName={projectTitle} 
                            onProjectNameChange={(newName) => {
                              // タブタイトルを更新
                              setActiveTab(prev => ({
                                ...prev,
                                title: newName,
                                content: newName
                              }));
                              // プロジェクトリストを更新（サイドバーに反映）
                              setProjectListVersion(prev => prev + 1);
                            }}
                          />);
                        }}
                        onProjectCreate={handleCreateProject}
                      />);
                    }}
                    onDashboardClick={() => {
                      setActiveTab({ id: 'home', title: 'ダッシュボード', type: 'home', isPinned: true });
                      setCurrentView(<DashboardGrid />);
                    }}
                    onEditorClick={() => {
                      setActiveTab({ id: 'editor', title: 'エディター', type: 'system' });
                      setCurrentView(<Editor />);
                    }}
                    onChatClick={() => {
                      setActiveTab({ id: 'chat', title: 'チャット', type: 'system' });
                      setCurrentView(<ISSystem />);
                    }}
                  />
                );
                title = 'システム概要';
                break;
              case 'バックアップ':
                view = (
                  <div className="h-full p-6 bg-base-100">
                    <div className="max-w-4xl mx-auto">
                      <h1 className="text-2xl font-bold mb-6 flex items-center gap-3">
                        💾 バックアップ管理
                        <span className="badge badge-warning">開発中</span>
                      </h1>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="card bg-base-200 shadow-xl">
                          <div className="card-body">
                            <h2 className="card-title text-primary">手動バックアップ</h2>
                            <p className="text-sm text-base-content/70 mb-4">
                              現在のプロジェクトを即座にバックアップ
                            </p>
                            <button className="btn btn-primary btn-sm">
                              今すぐバックアップ
                            </button>
                          </div>
                        </div>
                        
                        <div className="card bg-base-200 shadow-xl">
                          <div className="card-body">
                            <h2 className="card-title text-secondary">自動バックアップ</h2>
                            <p className="text-sm text-base-content/70 mb-4">
                              定期的な自動バックアップスケジュール
                            </p>
                            <button className="btn btn-secondary btn-sm">
                              設定（準備中）
                            </button>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-8">
                        <h3 className="text-lg font-semibold mb-4">バックアップ履歴</h3>
                        <div className="bg-base-200 rounded-lg p-4 text-center text-base-content/60">
                          バックアップ履歴は実装中です...
                        </div>
                      </div>
                    </div>
                  </div>
                );
                break;
              case 'エージェント管理':
                view = <AgentDashboard />;
                break;
              case 'ブラウザ自動化':
                view = <BrowserAutomationPanel />;
                break;
              case '対談スタジオ':
                view = <div>対談スタジオ（一時的に無効化）</div>;
                break;
              case '設定':
                view = <SettingsPanel />;
                break;
              case 'プラグイン':
                view = <PluginStore />;
                break;
              case '統計':
                view = <StatsDashboard />;
                break;
              case 'Expo QR':
                view = <ExpoQRView />;
                title = 'Expo QR';
                break;
              case 'フォルダ管理':
                view = (
                  <FileExplorer 
                    onFileSelect={(file) => console.log('ファイル選択:', file)}
                    onFileOpen={(file) => {
                      // ファイルを新しいタブで開く
                      const newTab = {
                        id: `file-${file.id}`,
                        title: file.name,
                        type: 'system' as const,
                        content: file.path
                      };
                      setActiveTab(newTab);
                      setCurrentView(
                        <div className="h-full p-6 bg-base-100">
                          <div className="max-w-4xl mx-auto">
                            <h1 className="text-2xl font-bold mb-4 flex items-center gap-3">
                              📄 {file.name}
                            </h1>
                            <div className="bg-base-200 rounded-lg p-4">
                              <p className="text-sm text-base-content/70 mb-2">ファイルパス: {file.path}</p>
                              <p className="text-sm text-base-content/70">
                                実際のファイル内容の表示機能は今後実装予定です
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    }}
                  />
                );
                break;
              case 'ノート管理':
                view = <NoteManager />;
                break;
              case 'ログ管理':
                view = (
                  <div className="h-full p-6 bg-base-100">
                    <div className="max-w-4xl mx-auto">
                      <h1 className="text-2xl font-bold mb-6 flex items-center gap-3">
                        📋 ログ管理
                        <span className="badge badge-info">ベータ</span>
                      </h1>
                      
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="card bg-base-200 shadow-xl">
                          <div className="card-body">
                            <h2 className="card-title text-primary">システムログ</h2>
                            <div className="bg-base-300 rounded-lg p-4 font-mono text-sm max-h-64 overflow-y-auto">
                              <div className="text-success">[INFO] Meta Studio 起動完了</div>
                              <div className="text-info">[DEBUG] タブマネージャー初期化</div>
                              <div className="text-warning">[WARN] 一部機能は開発中です</div>
                              <div className="text-error">[ERROR] 模擬ログエントリ</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="card bg-base-200 shadow-xl">
                          <div className="card-body">
                            <h2 className="card-title text-secondary">プロジェクトアクティビティ</h2>
                            <div className="space-y-3">
                              <div className="flex items-center gap-3 p-2 bg-base-300/50 rounded">
                                <div className="w-2 h-2 bg-success rounded-full"></div>
                                <span className="text-sm">プロジェクト作成: 瞑想アプリ_projext</span>
                              </div>
                              <div className="flex items-center gap-3 p-2 bg-base-300/50 rounded">
                                <div className="w-2 h-2 bg-info rounded-full"></div>
                                <span className="text-sm">ファイル編集: App.tsx</span>
                              </div>
                              <div className="flex items-center gap-3 p-2 bg-base-300/50 rounded">
                                <div className="w-2 h-2 bg-warning rounded-full"></div>
                                <span className="text-sm">ビルド実行</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
                break;
              case 'パフォーマンス':
                view = (
                  <div className="h-full p-6 bg-base-100">
                    <div className="max-w-6xl mx-auto">
                      <h1 className="text-2xl font-bold mb-6 flex items-center gap-3">
                        ⚡ パフォーマンス監視
                        <span className="badge badge-success">リアルタイム</span>
                      </h1>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div className="stat bg-base-200 rounded-lg">
                          <div className="stat-title">CPU使用率</div>
                          <div className="stat-value text-2xl text-info">23%</div>
                          <div className="stat-desc">正常範囲</div>
                        </div>
                        <div className="stat bg-base-200 rounded-lg">
                          <div className="stat-title">メモリ使用量</div>
                          <div className="stat-value text-2xl text-warning">2.1GB</div>
                          <div className="stat-desc">4GB中</div>
                        </div>
                        <div className="stat bg-base-200 rounded-lg">
                          <div className="stat-title">ネットワーク</div>
                          <div className="stat-value text-2xl text-success">50MB/s</div>
                          <div className="stat-desc">アップロード</div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="card bg-base-200 shadow-xl">
                          <div className="card-body">
                            <h2 className="card-title">プロセス監視</h2>
                            <div className="overflow-x-auto">
                              <table className="table table-xs">
                                <thead>
                                  <tr>
                                    <th>プロセス</th>
                                    <th>CPU</th>
                                    <th>メモリ</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td>Next.js Dev</td>
                                    <td>15%</td>
                                    <td>512MB</td>
                                  </tr>
                                  <tr>
                                    <td>TypeScript</td>
                                    <td>8%</td>
                                    <td>256MB</td>
                                  </tr>
                                  <tr>
                                    <td>Meta Studio</td>
                                    <td>3%</td>
                                    <td>128MB</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                        
                        <div className="card bg-base-200 shadow-xl">
                          <div className="card-body">
                            <h2 className="card-title">最適化提案</h2>
                            <div className="space-y-3">
                              <div className="alert alert-info alert-sm">
                                <span>💡 未使用のパッケージを削除してビルドサイズを最適化</span>
                              </div>
                              <div className="alert alert-warning alert-sm">
                                <span>⚠️ 大きな画像ファイルを圧縮することを推奨</span>
                              </div>
                              <div className="alert alert-success alert-sm">
                                <span>✅ React dev toolsを本番環境から除外済み</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
                break;
              case 'タスクマネージャー':
                view = <TaskManager />;
                break;
              default:
                view = (
                  <SystemPlaceholder 
                    title={system} 
                    description="システム機能の実装を予定しています"
                    icon="⚙️"
                    priority="medium"
                    estimatedCompletion="未定"
                    plannedFeatures={[
                      "基本機能の実装",
                      "ユーザーインターフェース設計",
                      "データ管理機能",
                      "設定・カスタマイズ機能"
                    ]}
                  />
                );
            }
            
            setActiveTab({ 
              id: `system-${system}`, 
              title, 
              type: 'system',
              content: system
            });
            // 即座にビューを設定（クリック反応を保証）
            if (view) {
              setCurrentView(view);
            }
          }}
        />
        </ResizablePanel>
      )}

      {/* メインコンテンツエリア */}
      <div className="flex-1 flex flex-col">
        {/* メインエディタ（上部）- ターミナル高さに応じて動的調整・レスポンシブ対応 */}
        <div 
          className="flex flex-1 layout-transition"
          style={{ 
            height: !isMobile ? `calc(100vh - ${terminalHeight}px - 60px)` : 'auto',
            minHeight: !isMobile ? '200px' : 'auto',
            maxHeight: !isMobile ? `calc(100vh - 150px - 60px)` : 'auto'
          }}
        >
          <TabManager 
            activeTab={activeTab}
            onTabChange={(tab) => {
              console.log('TabManager onTabChange called:', tab);
              console.log('TabManager onTabChange - tab type:', tab.type);
              console.log('TabManager onTabChange - tab content:', tab.content);
              
              if (tab.type === 'home') {
                console.log('TabManager: Setting DashboardGrid for home');
                setCurrentView(<DashboardGrid />);
              } else if (tab.type === 'dashboard') {
                console.log('TabManager: Setting XtilesDashboard');
                setCurrentView(<XtilesDashboard />);
              } else if (tab.type === 'project') {
                console.log('TabManager: Handling project tab:', tab);
                console.log('TabManager: Project content:', tab.content);
                
                // プロジェクトタブの場合は即座にProjectViewを作成・表示
                const projectName = tab.content || tab.title;
                console.log('TabManager: Creating ProjectView for:', projectName);
                
                const projectView = <ProjectView 
                  key={`project-view-${projectName}-${Date.now()}`} // 強制リレンダリング
                  projectName={projectName} 
                  onProjectNameChange={(newName) => {
                    console.log('TabManager: Project name change:', { oldName: projectName, newName });
                    // タブタイトルを更新
                    setActiveTab(prev => prev ? {
                      ...prev,
                      title: newName,
                      content: newName
                    } : null);
                    // プロジェクト名変更をSidebarに通知
                    setProjectNameUpdate({ oldName: projectName, newName, timestamp: Date.now() });
                  }}
                />;
                
                console.log('TabManager: Setting ProjectView in currentView');
                setCurrentView(projectView);
                console.log('TabManager: ProjectView set successfully');
              } else if (tab.type === 'file') {
                // ファイルタブの場合はFileViewerを表示
                setCurrentView(<FileViewer fileName={tab.title} filePath={tab.content} />);
              } else if (tab.type === 'launcher') {
                setCurrentView(<LauncherView onProjectClick={(projectId, projectTitle) => {
                  setActiveTab({ 
                    id: `project-${projectId}`, 
                    title: projectTitle, 
                    type: 'project',
                    content: projectTitle 
                  });
                  setCurrentView(<ProjectView 
                    projectName={projectTitle} 
                    onProjectNameChange={(newName) => {
                      // タブタイトルを更新
                      setActiveTab(prev => ({
                        ...prev,
                        title: newName,
                        content: newName
                      }));
                      // プロジェクトリストを更新（サイドバーに反映）
                      setProjectListVersion(prev => prev + 1);
                    }}
                  />);
                }} />);
              } else if (tab.type === 'system') {
                const systemName = tab.title;
                let view = null;
                
                switch (systemName) {
                  case 'エディター':
                    view = <Editor />;
                    break;
                  case 'インボックス':
                    view = <InboxSystem />;
                    break;
                  case '管理ダッシュボード':
                  case '管理':
                  case 'メタスタジオ':
                    view = <MetaStudioDashboard />;
                    break;
                  case 'バックアップ':
                    view = (
                      <div className="h-full p-6 bg-base-100">
                        <div className="max-w-4xl mx-auto">
                          <h1 className="text-2xl font-bold mb-6 flex items-center gap-3">
                            💾 バックアップ管理
                            <span className="badge badge-warning">開発中</span>
                          </h1>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="card bg-base-200 shadow-xl">
                              <div className="card-body">
                                <h2 className="card-title text-primary">手動バックアップ</h2>
                                <p className="text-sm text-base-content/70 mb-4">
                                  現在のプロジェクトを即座にバックアップ
                                </p>
                                <button className="btn btn-primary btn-sm">
                                  今すぐバックアップ
                                </button>
                              </div>
                            </div>
                            
                            <div className="card bg-base-200 shadow-xl">
                              <div className="card-body">
                                <h2 className="card-title text-secondary">自動バックアップ</h2>
                                <p className="text-sm text-base-content/70 mb-4">
                                  定期的な自動バックアップスケジュール
                                </p>
                                <button className="btn btn-secondary btn-sm">
                                  設定（準備中）
                                </button>
                              </div>
                            </div>
                          </div>
                          
                          <div className="mt-8">
                            <h3 className="text-lg font-semibold mb-4">バックアップ履歴</h3>
                            <div className="bg-base-200 rounded-lg p-4 text-center text-base-content/60">
                              バックアップ履歴は実装中です...
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                    break;
                  case 'エージェント管理':
                    view = <AgentDashboard />;
                    break;
                  case 'ブラウザ自動化':
                    view = <BrowserAutomationPanel />;
                    break;
                  case '対談スタジオ':
                    view = <div>対談スタジオ（一時的に無効化）</div>;
                    break;
                  case '設定':
                    view = <SettingsPanel />;
                    break;
                  case 'プラグイン':
                    view = <PluginStore />;
                    break;
                  case '統計':
                    view = <StatsDashboard />;
                    break;
                  case 'Expo QR':
                    view = <ExpoQRView />;
                    break;
                  case 'フォルダ管理':
                    view = (
                      <FileExplorer 
                        onFileSelect={(file) => console.log('ファイル選択:', file)}
                        onFileOpen={(file) => {
                          // ファイルを新しいタブで開く
                          const newTab = {
                            id: `file-${file.id}`,
                            title: file.name,
                            type: 'system' as const,
                            content: file.path
                          };
                          setActiveTab(newTab);
                          setCurrentView(
                            <div className="h-full p-6 bg-base-100">
                              <div className="max-w-4xl mx-auto">
                                <h1 className="text-2xl font-bold mb-4 flex items-center gap-3">
                                  📄 {file.name}
                                </h1>
                                <div className="bg-base-200 rounded-lg p-4">
                                  <p className="text-sm text-base-content/70 mb-2">ファイルパス: {file.path}</p>
                                  <p className="text-sm text-base-content/70">
                                    実際のファイル内容の表示機能は今後実装予定です
                                  </p>
                                </div>
                              </div>
                            </div>
                          );
                        }}
                      />
                    );
                    break;
                  case 'ノート管理':
                    view = <NoteManager />;
                    break;
                  case 'ログ管理':
                    view = (
                      <div className="h-full p-6 bg-base-100">
                        <div className="max-w-4xl mx-auto">
                          <h1 className="text-2xl font-bold mb-6 flex items-center gap-3">
                            📋 ログ管理
                            <span className="badge badge-info">ベータ</span>
                          </h1>
                          
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="card bg-base-200 shadow-xl">
                              <div className="card-body">
                                <h2 className="card-title text-primary">システムログ</h2>
                                <div className="bg-base-300 rounded-lg p-4 font-mono text-sm max-h-64 overflow-y-auto">
                                  <div className="text-success">[INFO] Meta Studio 起動完了</div>
                                  <div className="text-info">[DEBUG] タブマネージャー初期化</div>
                                  <div className="text-warning">[WARN] 一部機能は開発中です</div>
                                  <div className="text-error">[ERROR] 模擬ログエントリ</div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="card bg-base-200 shadow-xl">
                              <div className="card-body">
                                <h2 className="card-title text-secondary">プロジェクトアクティビティ</h2>
                                <div className="space-y-3">
                                  <div className="flex items-center gap-3 p-2 bg-base-300/50 rounded">
                                    <div className="w-2 h-2 bg-success rounded-full"></div>
                                    <span className="text-sm">プロジェクト作成: 瞑想アプリ_projext</span>
                                  </div>
                                  <div className="flex items-center gap-3 p-2 bg-base-300/50 rounded">
                                    <div className="w-2 h-2 bg-info rounded-full"></div>
                                    <span className="text-sm">ファイル編集: App.tsx</span>
                                  </div>
                                  <div className="flex items-center gap-3 p-2 bg-base-300/50 rounded">
                                    <div className="w-2 h-2 bg-warning rounded-full"></div>
                                    <span className="text-sm">ビルド実行</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                    break;
                  case 'パフォーマンス':
                    view = (
                      <div className="h-full p-6 bg-base-100">
                        <div className="max-w-6xl mx-auto">
                          <h1 className="text-2xl font-bold mb-6 flex items-center gap-3">
                            ⚡ パフォーマンス監視
                            <span className="badge badge-success">リアルタイム</span>
                          </h1>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            <div className="stat bg-base-200 rounded-lg">
                              <div className="stat-title">CPU使用率</div>
                              <div className="stat-value text-2xl text-info">23%</div>
                              <div className="stat-desc">正常範囲</div>
                            </div>
                            <div className="stat bg-base-200 rounded-lg">
                              <div className="stat-title">メモリ使用量</div>
                              <div className="stat-value text-2xl text-warning">2.1GB</div>
                              <div className="stat-desc">4GB中</div>
                            </div>
                            <div className="stat bg-base-200 rounded-lg">
                              <div className="stat-title">ネットワーク</div>
                              <div className="stat-value text-2xl text-success">50MB/s</div>
                              <div className="stat-desc">アップロード</div>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="card bg-base-200 shadow-xl">
                              <div className="card-body">
                                <h2 className="card-title">プロセス監視</h2>
                                <div className="overflow-x-auto">
                                  <table className="table table-xs">
                                    <thead>
                                      <tr>
                                        <th>プロセス</th>
                                        <th>CPU</th>
                                        <th>メモリ</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr>
                                        <td>Next.js Dev</td>
                                        <td>15%</td>
                                        <td>512MB</td>
                                      </tr>
                                      <tr>
                                        <td>TypeScript</td>
                                        <td>8%</td>
                                        <td>256MB</td>
                                      </tr>
                                      <tr>
                                        <td>Meta Studio</td>
                                        <td>3%</td>
                                        <td>128MB</td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                            
                            <div className="card bg-base-200 shadow-xl">
                              <div className="card-body">
                                <h2 className="card-title">最適化提案</h2>
                                <div className="space-y-3">
                                  <div className="alert alert-info alert-sm">
                                    <span>💡 未使用のパッケージを削除してビルドサイズを最適化</span>
                                  </div>
                                  <div className="alert alert-warning alert-sm">
                                    <span>⚠️ 大きな画像ファイルを削除してメモリ使用量を削減</span>
                                  </div>
                                  <div className="alert alert-success alert-sm">
                                    <span>✅ React dev toolsを本番環境から除外済み</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                    break;
                  default:
                    view = (
                      <SystemPlaceholder 
                        title={systemName} 
                        description="システム機能の実装を予定しています"
                        icon="⚙️"
                        priority="medium"
                        estimatedCompletion="未定"
                        plannedFeatures={[
                          "基本機能の実装",
                          "ユーザーインターフェース設計",
                          "データ管理機能",
                          "設定・カスタマイズ機能"
                        ]}
                      />
                    );
                }
                
                // タブ設定はTabManagerのonTabChangeで処理される
              }
            }}
          >
            {currentView}
          </TabManager>
        </div>

        {/* 下部ターミナルエリア（完全固定位置・タブ間統一・無制限リサイズ） */}
        {!isMobile && (
          <div 
            className="bg-base-300 shadow-lg border-t border-base-content/10 relative"
            style={{ height: `${terminalHeight}px` }}
          >
            {/* リサイザーハンドル - スムーズリサイズ対応 */}
            <div
              className="resize-handle absolute top-0 left-0 w-full h-3 bg-base-content/20 hover:bg-primary/50 active:bg-primary/70 cursor-row-resize group shadow-sm z-10"
              onMouseDown={(e) => {
                const startY = e.clientY;
                const startHeight = terminalHeight;
                
                // リサイズ中のビジュアルフィードバック
                document.body.style.cursor = 'row-resize';
                document.body.style.userSelect = 'none';
                document.body.classList.add('resize-active');
                
                const handleMouseMove = (e: MouseEvent) => {
                  const deltaY = startY - e.clientY; // 上にドラッグで高さ増加
                  const maxHeight = Math.min(window.innerHeight * 0.8, window.innerHeight - 200);
                  const minHeight = 150;
                  const newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + deltaY));
                  
                  // リアルタイムでターミナル高さとメインペインを更新
                  setTerminalHeight(newHeight);
                };
                
                const handleMouseUp = () => {
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);
                  document.body.style.cursor = '';
                  document.body.style.userSelect = '';
                  document.body.classList.remove('resize-active');
                  
                  // リサイズ完了時にlocalStorageに保存
                  if (typeof window !== 'undefined') {
                    localStorage.setItem('meta-studio-terminal-height', terminalHeight.toString());
                  }
                };
                
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
              }}
              title="ターミナルの高さを調整"
            >
              {/* リサイザー視覚インジケーター */}
              <div className="absolute left-1/2 top-1/2 h-0.5 w-8 -translate-x-1/2 -translate-y-1/2 bg-base-content/40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
            
            <div className="h-full pt-2">
              <MetaTerminal />
            </div>
          </div>
        )}
      </div>

      {/* 右サイドバー（完全固定位置・画面全体の高さ・一貫性保証） */}
      {!isMobile && (
        <ResizablePanel 
          direction="horizontal" 
          initialSize={rightSidebarWidth} 
          minSize={350} 
          maxSize={600}
          className="bg-base-200 shadow-lg border-l border-base-content/10"
          handlePosition="start"
          storageKey="meta-studio-right-sidebar"
        >
          <ISSystem />
        </ResizablePanel>
      )}

      {/* モバイル用ボトムナビゲーション */}
      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 bg-base-200 border-t border-base-content/10 p-2 z-40">
          <div className="flex justify-around">
            <button 
              className="btn btn-ghost btn-sm"
              onClick={() => setIsLauncherOpen(true)}
            >
              🚀
            </button>
            <button 
              className="btn btn-ghost btn-sm"
              onClick={() => {
                setActiveTab({ id: 'dashboard', title: 'ダッシュボード', type: 'dashboard' });
                setCurrentView(<DashboardGrid />);
              }}
            >
              📊
            </button>
            <button 
              className="btn btn-ghost btn-sm"
              onClick={() => {
                setActiveTab({ id: 'chat', title: 'チャット', type: 'chat' });
                setCurrentView(<ISSystem />);
              }}
            >
              💬
            </button>
          </div>
        </div>
      )}

      {/* モーダルランチャー（Cmd+K用） */}
      <Launcher 
        isOpen={isLauncherOpen} 
        onClose={() => setIsLauncherOpen(false)}
        onCreateProject={handleCreateProject}
      />

      {/* グローバルランチャーボタン（左下のNext.js右横に配置） */}
      <button
        onClick={() => setIsLauncherOpen(true)}
        className="fixed bottom-4 left-20 btn btn-circle btn-primary shadow-lg z-40"
        title="ランチャーを開く (Cmd+K)"
      >
        <Rocket size={20} />
      </button>


      {/* VRMデバッグパネル - 無効化（競合回避のため） */}
      {/* <VRMDebugPanel 
        isVisible={isDebugPanelVisible}
        onToggle={() => setIsDebugPanelVisible(!isDebugPanelVisible)}
      /> */}
      </div>
    </CharacterProvider>
  );
}