// 通知システム
// トースト通知、システム通知、進捗通知を統合管理

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle, Loader } from 'lucide-react';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'loading';
  title: string;
  message?: string;
  duration?: number; // ms, 0で永続表示
  action?: {
    label: string;
    onClick: () => void;
  };
  progress?: number; // 0-100
  timestamp: number;
}

interface NotificationSystemProps {
  maxNotifications?: number;
  defaultDuration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  className?: string;
}

export default function NotificationSystem({
  maxNotifications = 5,
  defaultDuration = 5000,
  position = 'top-right',
  className = ''
}: NotificationSystemProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // 通知を追加
  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: Date.now(),
      duration: notification.duration ?? defaultDuration
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      // 最大数を超えた場合は古いものを削除
      return updated.slice(0, maxNotifications);
    });

    // 自動削除（duration > 0の場合）
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, [defaultDuration, maxNotifications]);

  // 通知を削除
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // 全通知をクリア
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // 通知を更新（主に進捗更新用）
  const updateNotification = useCallback((id: string, updates: Partial<Notification>) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, ...updates } : n)
    );
  }, []);

  // アイコンを取得
  const getIcon = (type: Notification['type']) => {
    const iconProps = { size: 20 };
    
    switch (type) {
      case 'success':
        return <CheckCircle {...iconProps} className="text-success" />;
      case 'error':
        return <AlertCircle {...iconProps} className="text-error" />;
      case 'warning':
        return <AlertTriangle {...iconProps} className="text-warning" />;
      case 'info':
        return <Info {...iconProps} className="text-info" />;
      case 'loading':
        return <Loader {...iconProps} className="text-primary animate-spin" />;
      default:
        return <Info {...iconProps} className="text-base-content" />;
    }
  };

  // 通知スタイルを取得
  const getNotificationStyle = (type: Notification['type']) => {
    const baseStyle = "border-l-4 bg-base-100 shadow-lg";
    
    switch (type) {
      case 'success':
        return `${baseStyle} border-success`;
      case 'error':
        return `${baseStyle} border-error`;
      case 'warning':
        return `${baseStyle} border-warning`;
      case 'info':
        return `${baseStyle} border-info`;
      case 'loading':
        return `${baseStyle} border-primary`;
      default:
        return `${baseStyle} border-base-content`;
    }
  };

  // 位置スタイルを取得
  const getPositionStyle = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  // グローバル通知関数をwindowに登録
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).notify = {
        success: (title: string, message?: string, options?: Partial<Notification>) =>
          addNotification({ type: 'success', title, message, ...options }),
        error: (title: string, message?: string, options?: Partial<Notification>) =>
          addNotification({ type: 'error', title, message, ...options }),
        warning: (title: string, message?: string, options?: Partial<Notification>) =>
          addNotification({ type: 'warning', title, message, ...options }),
        info: (title: string, message?: string, options?: Partial<Notification>) =>
          addNotification({ type: 'info', title, message, ...options }),
        loading: (title: string, message?: string, options?: Partial<Notification>) =>
          addNotification({ type: 'loading', title, message, duration: 0, ...options }),
        update: updateNotification,
        remove: removeNotification,
        clear: clearAllNotifications
      };
    }

    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).notify;
      }
    };
  }, [addNotification, updateNotification, removeNotification, clearAllNotifications]);

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className={`fixed z-50 ${getPositionStyle()} ${className}`}>
      <div className="space-y-2 w-80">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`${getNotificationStyle(notification.type)} rounded-lg p-4 transition-all duration-300 transform hover:scale-105`}
          >
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 mt-0.5">
                {getIcon(notification.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm">{notification.title}</div>
                {notification.message && (
                  <div className="text-xs text-base-content/70 mt-1">
                    {notification.message}
                  </div>
                )}
                
                {/* 進捗バー */}
                {typeof notification.progress === 'number' && (
                  <div className="mt-2">
                    <div className="flex justify-between text-xs text-base-content/60 mb-1">
                      <span>進捗</span>
                      <span>{notification.progress}%</span>
                    </div>
                    <div className="w-full bg-base-300 rounded-full h-1.5">
                      <div
                        className="bg-primary h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${notification.progress}%` }}
                      />
                    </div>
                  </div>
                )}
                
                {/* アクションボタン */}
                {notification.action && (
                  <button
                    className="btn btn-xs btn-outline mt-2"
                    onClick={notification.action.onClick}
                  >
                    {notification.action.label}
                  </button>
                )}
              </div>
              
              <button
                className="flex-shrink-0 btn btn-ghost btn-xs btn-circle"
                onClick={() => removeNotification(notification.id)}
              >
                <X size={14} />
              </button>
            </div>
          </div>
        ))}
        
        {/* 全クリアボタン（複数通知がある場合） */}
        {notifications.length > 1 && (
          <div className="text-center">
            <button
              className="btn btn-xs btn-outline"
              onClick={clearAllNotifications}
            >
              全て削除
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// 便利な通知関数をエクスポート
export const notify = {
  success: (title: string, message?: string, options?: Partial<Notification>) => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      return (window as any).notify.success(title, message, options);
    }
  },
  error: (title: string, message?: string, options?: Partial<Notification>) => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      return (window as any).notify.error(title, message, options);
    }
  },
  warning: (title: string, message?: string, options?: Partial<Notification>) => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      return (window as any).notify.warning(title, message, options);
    }
  },
  info: (title: string, message?: string, options?: Partial<Notification>) => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      return (window as any).notify.info(title, message, options);
    }
  },
  loading: (title: string, message?: string, options?: Partial<Notification>) => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      return (window as any).notify.loading(title, message, options);
    }
  },
  update: (id: string, updates: Partial<Notification>) => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      (window as any).notify.update(id, updates);
    }
  },
  remove: (id: string) => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      (window as any).notify.remove(id);
    }
  },
  clear: () => {
    if (typeof window !== 'undefined' && (window as any).notify) {
      (window as any).notify.clear();
    }
  }
};
