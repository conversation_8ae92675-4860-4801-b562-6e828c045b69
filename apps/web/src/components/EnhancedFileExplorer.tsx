// 拡張ファイルエクスプローラー
// 名前変更、削除、D&D移動機能を実装

'use client';

import React, { useState, useRef, useCallback } from 'react';
import { 
  Folder, 
  File, 
  Edit3, 
  Trash2, 
  Move, 
  Copy, 
  Download, 
  Upload,
  Plus,
  MoreHorizontal,
  Check,
  X,
  FolderPlus,
  FilePlus
} from 'lucide-react';

interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified?: string;
  children?: FileItem[];
}

interface EnhancedFileExplorerProps {
  files: FileItem[];
  currentPath: string;
  onPathChange: (path: string) => void;
  onFileSelect?: (file: FileItem) => void;
  onFileAction?: (action: string, file: FileItem, newName?: string) => void;
  className?: string;
}

export default function EnhancedFileExplorer({
  files,
  currentPath,
  onPathChange,
  onFileSelect,
  onFileAction,
  className = ''
}: EnhancedFileExplorerProps) {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [draggedItem, setDraggedItem] = useState<FileItem | null>(null);
  const [dropTarget, setDropTarget] = useState<string | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    item: FileItem;
  } | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState<{
    type: 'file' | 'folder';
    name: string;
  } | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // ファイル選択処理
  const handleItemClick = (item: FileItem, event: React.MouseEvent) => {
    if (event.ctrlKey || event.metaKey) {
      // 複数選択
      const newSelected = new Set(selectedItems);
      if (newSelected.has(item.path)) {
        newSelected.delete(item.path);
      } else {
        newSelected.add(item.path);
      }
      setSelectedItems(newSelected);
    } else {
      // 単一選択
      setSelectedItems(new Set([item.path]));
      
      if (item.type === 'directory') {
        onPathChange(item.path);
      } else {
        onFileSelect?.(item);
      }
    }
  };

  // ダブルクリック処理
  const handleItemDoubleClick = (item: FileItem) => {
    if (item.type === 'directory') {
      onPathChange(item.path);
    } else {
      onFileSelect?.(item);
    }
  };

  // 右クリックメニュー
  const handleContextMenu = (event: React.MouseEvent, item: FileItem) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      item
    });
  };

  // 名前変更開始
  const startRename = (item: FileItem) => {
    setEditingItem(item.path);
    setEditingName(item.name);
    setContextMenu(null);
  };

  // 名前変更確定
  const confirmRename = () => {
    if (editingItem && editingName.trim()) {
      const item = files.find(f => f.path === editingItem);
      if (item) {
        onFileAction?.('rename', item, editingName.trim());
      }
    }
    setEditingItem(null);
    setEditingName('');
  };

  // 名前変更キャンセル
  const cancelRename = () => {
    setEditingItem(null);
    setEditingName('');
  };

  // ドラッグ開始
  const handleDragStart = (event: React.DragEvent, item: FileItem) => {
    setDraggedItem(item);
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', item.path);
  };

  // ドラッグオーバー
  const handleDragOver = (event: React.DragEvent, item: FileItem) => {
    if (item.type === 'directory' && draggedItem?.path !== item.path) {
      event.preventDefault();
      setDropTarget(item.path);
    }
  };

  // ドラッグリーブ
  const handleDragLeave = () => {
    setDropTarget(null);
  };

  // ドロップ
  const handleDrop = (event: React.DragEvent, targetItem: FileItem) => {
    event.preventDefault();
    
    if (draggedItem && targetItem.type === 'directory' && draggedItem.path !== targetItem.path) {
      onFileAction?.('move', draggedItem, targetItem.path);
    }
    
    setDraggedItem(null);
    setDropTarget(null);
  };

  // ファイル削除
  const deleteItem = (item: FileItem) => {
    if (confirm(`"${item.name}"を削除しますか？`)) {
      onFileAction?.('delete', item);
    }
    setContextMenu(null);
  };

  // ファイル/フォルダ作成
  const createItem = (type: 'file' | 'folder', name: string) => {
    if (name.trim()) {
      onFileAction?.('create', { 
        name: name.trim(), 
        path: `${currentPath}/${name.trim()}`, 
        type 
      } as FileItem);
    }
    setShowCreateDialog(null);
  };

  // ファイルアップロード
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        onFileAction?.('upload', {
          name: file.name,
          path: `${currentPath}/${file.name}`,
          type: 'file',
          size: file.size
        } as FileItem);
      });
    }
  };

  // ファイルサイズフォーマット
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // ファイルアイコン取得
  const getFileIcon = (item: FileItem) => {
    if (item.type === 'directory') {
      return <Folder size={16} className="text-blue-500" />;
    }
    
    const ext = item.name.split('.').pop()?.toLowerCase();
    const iconClass = "text-gray-500";
    
    switch (ext) {
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
        return <File size={16} className="text-yellow-500" />;
      case 'css':
      case 'scss':
      case 'sass':
        return <File size={16} className="text-blue-500" />;
      case 'html':
      case 'htm':
        return <File size={16} className="text-orange-500" />;
      case 'json':
        return <File size={16} className="text-green-500" />;
      case 'md':
        return <File size={16} className="text-purple-500" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <File size={16} className="text-pink-500" />;
      default:
        return <File size={16} className={iconClass} />;
    }
  };

  return (
    <div className={`enhanced-file-explorer ${className}`}>
      {/* ツールバー */}
      <div className="flex items-center gap-2 p-2 border-b border-base-content/10 bg-base-200/50">
        <button
          className="btn btn-xs btn-outline"
          onClick={() => setShowCreateDialog({ type: 'folder', name: '' })}
          title="新しいフォルダ"
        >
          <FolderPlus size={14} />
        </button>
        <button
          className="btn btn-xs btn-outline"
          onClick={() => setShowCreateDialog({ type: 'file', name: '' })}
          title="新しいファイル"
        >
          <FilePlus size={14} />
        </button>
        <button
          className="btn btn-xs btn-outline"
          onClick={() => fileInputRef.current?.click()}
          title="ファイルアップロード"
        >
          <Upload size={14} />
        </button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileUpload}
        />
        
        <div className="flex-1" />
        
        <div className="text-xs text-base-content/60">
          {selectedItems.size > 0 && `${selectedItems.size}個選択中`}
        </div>
      </div>

      {/* ファイルリスト */}
      <div className="flex-1 overflow-auto">
        {files.map((item) => (
          <div
            key={item.path}
            className={`flex items-center gap-2 p-2 hover:bg-base-200/50 cursor-pointer transition-colors ${
              selectedItems.has(item.path) ? 'bg-primary/20' : ''
            } ${
              dropTarget === item.path ? 'bg-success/20 border-l-4 border-success' : ''
            }`}
            onClick={(e) => handleItemClick(item, e)}
            onDoubleClick={() => handleItemDoubleClick(item)}
            onContextMenu={(e) => handleContextMenu(e, item)}
            draggable
            onDragStart={(e) => handleDragStart(e, item)}
            onDragOver={(e) => handleDragOver(e, item)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, item)}
          >
            {getFileIcon(item)}
            
            <div className="flex-1 min-w-0">
              {editingItem === item.path ? (
                <div className="flex items-center gap-1">
                  <input
                    type="text"
                    value={editingName}
                    onChange={(e) => setEditingName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') confirmRename();
                      if (e.key === 'Escape') cancelRename();
                    }}
                    onBlur={confirmRename}
                    className="input input-xs flex-1"
                    autoFocus
                  />
                  <button
                    className="btn btn-xs btn-success"
                    onClick={confirmRename}
                  >
                    <Check size={12} />
                  </button>
                  <button
                    className="btn btn-xs btn-error"
                    onClick={cancelRename}
                  >
                    <X size={12} />
                  </button>
                </div>
              ) : (
                <div>
                  <div className="text-sm truncate">{item.name}</div>
                  <div className="text-xs text-base-content/60 flex gap-2">
                    {item.size && <span>{formatFileSize(item.size)}</span>}
                    {item.modified && <span>{item.modified}</span>}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* コンテキストメニュー */}
      {contextMenu && (
        <div
          className="fixed z-50 bg-base-100 border border-base-content/20 rounded-lg shadow-lg py-1 min-w-[150px]"
          style={{ left: contextMenu.x, top: contextMenu.y }}
          onMouseLeave={() => setContextMenu(null)}
        >
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-base-200 flex items-center gap-2"
            onClick={() => startRename(contextMenu.item)}
          >
            <Edit3 size={14} />
            名前を変更
          </button>
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-base-200 flex items-center gap-2"
            onClick={() => onFileAction?.('copy', contextMenu.item)}
          >
            <Copy size={14} />
            コピー
          </button>
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-base-200 flex items-center gap-2"
            onClick={() => onFileAction?.('download', contextMenu.item)}
          >
            <Download size={14} />
            ダウンロード
          </button>
          <hr className="my-1 border-base-content/10" />
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-error/20 text-error flex items-center gap-2"
            onClick={() => deleteItem(contextMenu.item)}
          >
            <Trash2 size={14} />
            削除
          </button>
        </div>
      )}

      {/* 作成ダイアログ */}
      {showCreateDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-base-100 rounded-lg p-4 w-80">
            <h3 className="text-lg font-semibold mb-3">
              新しい{showCreateDialog.type === 'folder' ? 'フォルダ' : 'ファイル'}を作成
            </h3>
            <input
              type="text"
              placeholder="名前を入力..."
              value={showCreateDialog.name}
              onChange={(e) => setShowCreateDialog({ ...showCreateDialog, name: e.target.value })}
              onKeyDown={(e) => {
                if (e.key === 'Enter') createItem(showCreateDialog.type, showCreateDialog.name);
                if (e.key === 'Escape') setShowCreateDialog(null);
              }}
              className="input input-bordered w-full mb-3"
              autoFocus
            />
            <div className="flex gap-2 justify-end">
              <button
                className="btn btn-outline"
                onClick={() => setShowCreateDialog(null)}
              >
                キャンセル
              </button>
              <button
                className="btn btn-primary"
                onClick={() => createItem(showCreateDialog.type, showCreateDialog.name)}
                disabled={!showCreateDialog.name.trim()}
              >
                作成
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
