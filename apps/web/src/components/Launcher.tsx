'use client';

import { useState } from 'react';
import { Rocket, Plus, FileText, Mic, Code, Brain, Zap, X, ChevronRight, Clock, Star, Search, Folder, Command, Settings, BookOpen, Hammer } from 'lucide-react';
import { useRouter } from 'next/navigation';
import ProjectTemplateModal from './ProjectTemplateModal';
import ProjectWizard from './ProjectWizard';

interface LauncherItem {
  id: string;
  name: string;
  description: string;
  icon: any;
  action: string;
  color: string;
  bgColor?: string;
  borderColor?: string;
  shortcut?: string;
  isRunning?: boolean; // アプリ実行状態
  url?: string; // 起動URL
}

interface LauncherProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateProject?: (template: any, projectName: string) => void;
}

export default function Launcher({ isOpen, onClose, onCreateProject }: LauncherProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showProjectWizard, setShowProjectWizard] = useState(false);
  const [runningApps, setRunningApps] = useState<Set<string>>(new Set());
  const [hoveredApp, setHoveredApp] = useState<string | null>(null);

  // アプリ起動機能
  const launchApp = (item: LauncherItem) => {
    if (item.url) {
      window.open(item.url, '_blank');
      setRunningApps(prev => new Set([...prev, item.id]));
      console.log(`🚀 アプリ起動: ${item.name}`);
    } else if (item.action === 'create-projext') {
      setShowProjectWizard(true);
    } else if (item.action === 'create-project') {
      setShowTemplateModal(true);
    }
  };

  // プロジェクト編集機能
  const editProject = (item: LauncherItem) => {
    console.log(`🔨 プロジェクト編集: ${item.name}`);
    // TODO: プロジェクト編集画面を開く実装
    // 例：router.push(`/projects/${item.id}/edit`)
  };

  // アプリ停止機能
  const stopApp = (appId: string) => {
    setRunningApps(prev => {
      const newSet = new Set(prev);
      newSet.delete(appId);
      return newSet;
    });
    console.log(`⏹️ アプリ停止: ${appId}`);
  };

  const launcherItems: LauncherItem[] = [
    {
      id: 'projext-wizard',
      name: 'Projext作成ウィザード',
      description: '要件定義群を段階的に作成',
      icon: BookOpen,
      action: 'create-projext',
      color: 'text-primary',
      bgColor: 'bg-primary/10',
      borderColor: 'border-primary/30',
      shortcut: 'Cmd+W'
    },
    {
      id: 'new-project',
      name: '新規プロジェクト作成',
      description: 'テンプレートから迅速作成',
      icon: Plus,
      action: 'create-project',
      color: 'text-primary',
      bgColor: 'bg-primary/10',
      borderColor: 'border-primary/30',
      shortcut: 'Cmd+N'
    },
    {
      id: 'voice-input',
      name: '音声でアイデア投入',
      description: '思考を即座にキャプチャ',
      icon: Mic,
      action: '/voice',
      color: 'text-secondary',
      bgColor: 'bg-secondary/10',
      borderColor: 'border-secondary/30',
      shortcut: 'Cmd+Shift+V'
    },
    {
      id: 'open-editor',
      name: 'エディターを開く',
      description: 'Craft風リアルタイム編集',
      icon: FileText,
      action: '/editor',
      color: 'text-accent',
      bgColor: 'bg-accent/10',
      borderColor: 'border-accent/30',
      shortcut: 'Cmd+E'
    },
    {
      id: 'ai-chat',
      name: '母と対話',
      description: 'AI技術相談・戦略立案',
      icon: Brain,
      action: 'open-mother-chat',
      color: 'text-secondary',
      bgColor: 'bg-secondary/10',
      borderColor: 'border-secondary/30',
      shortcut: 'Cmd+M'
    },
    {
      id: 'px-terminal',
      name: 'pxターミナル',
      description: 'コマンドでプロジェクト管理',
      icon: Code,
      action: 'focus-terminal',
      color: 'text-info',
      bgColor: 'bg-info/10',
      borderColor: 'border-info/30',
      shortcut: 'Cmd+T'
    },
    {
      id: 'quick-deploy',
      name: 'クイックデプロイ',
      description: '現在のプロジェクトをデプロイ',
      icon: Zap,
      action: 'deploy',
      color: 'text-warning',
      bgColor: 'bg-warning/10',
      borderColor: 'border-warning/30',
      shortcut: 'Cmd+D'
    },
    {
      id: 'search-files',
      name: 'ファイル検索',
      description: 'プロジェクト内のファイルを検索',
      icon: Search,
      action: 'search-files',
      color: 'text-info',
      bgColor: 'bg-info/10',
      borderColor: 'border-info/30',
      shortcut: 'Cmd+P'
    },
    {
      id: 'file-manager',
      name: 'ファイル管理',
      description: 'プロジェクトファイルを管理',
      icon: Folder,
      action: 'file-manager',
      color: 'text-accent',
      bgColor: 'bg-accent/10',
      borderColor: 'border-accent/30',
      shortcut: 'Cmd+E'
    },
    // 実際に作成されたアプリ例（デモ用 - 実際のファイルアクセスは行わない）
    // これらは表示のみで、ファイルAPIにはアクセスしません
    {
      id: 'terminal-focus',
      name: 'ターミナルにフォーカス',
      description: 'ターミナルに移動してコマンド実行',
      icon: Command,
      action: 'focus-terminal',
      color: 'text-neutral',
      bgColor: 'bg-neutral/10',
      borderColor: 'border-neutral/30',
      shortcut: 'Cmd+`'
    },
    {
      id: 'settings',
      name: '設定を開く',
      description: 'メタスタジオの設定画面',
      icon: Settings,
      action: 'open-settings',
      color: 'text-base-content',
      bgColor: 'bg-base-content/10',
      borderColor: 'border-base-content/30',
      shortcut: 'Cmd+,'
    }
  ];

  const recentProjects = [
    { name: '瞑想アプリ_projext', lastOpened: '2時間前', progress: 75 },
    { name: '投資bot_projext', lastOpened: '5時間前', progress: 90 },
    { name: 'iS_streamer_projext', lastOpened: '1日前', progress: 25 },
  ];

  const recentCommands = [
    'px run build',
    'px test',
    'px deploy production',
    'px agents list',
    'px chat "新機能について"'
  ];

  const filteredItems = launcherItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAction = (action: string) => {
    if (action.startsWith('/')) {
      router.push(action);
      onClose();
    } else {
      // その他のアクション処理
      switch (action) {
        case 'create-projext':
          // Projext作成ウィザードを開く（ランチャーは閉じない）
          setShowProjectWizard(true);
          return; // onClose()を呼ばない
        case 'create-project':
          // プロジェクト作成モーダルを開く（ランチャーは閉じない）
          setShowTemplateModal(true);
          return; // onClose()を呼ばない
        case 'open-mother-chat':
          // 母チャットにフォーカス
          document.querySelector('.mother-chat')?.scrollIntoView({ behavior: 'smooth' });
          break;
        case 'focus-terminal':
          // ターミナルにフォーカス
          document.querySelector('.zellij-terminal')?.scrollIntoView({ behavior: 'smooth' });
          break;
        case 'deploy':
          // デプロイ処理
          console.log('デプロイ開始: 現在のプロジェクト');
          // TODO: 実際のデプロイロジックを実装
          break;
        case 'search-files':
          // ファイル検索機能（Cmd+P）
          const fileSearchEvent = new KeyboardEvent('keydown', {
            key: 'p',
            metaKey: true,
            bubbles: true
          });
          document.dispatchEvent(fileSearchEvent);
          break;
        case 'file-manager':
          // ファイル管理画面を開く
          router.push('/files');
          break;
        case 'open-settings':
          // 設定画面を開く
          router.push('/settings');
          break;
      }
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* オーバーレイ */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* ランチャー本体 */}
      <div className="relative w-full max-w-3xl mx-4 bg-base-100/95 rounded-2xl shadow-2xl neo-depth overflow-hidden">
        {/* ヘッダー */}
        <div className="p-6 border-b border-base-content/10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Rocket size={24} className="text-primary" />
              <h2 className="text-2xl font-bold">クイックランチャー</h2>
            </div>
            <button
              onClick={onClose}
              className="btn btn-sm btn-ghost btn-circle"
            >
              <X size={20} />
            </button>
          </div>

          <p className="text-sm text-base-content/60 mb-4">
            コマンド・検索・クイックアクションの実行
          </p>
          
          {/* 検索バー */}
          <input
            type="text"
            placeholder="アクションを検索..."
            className="input input-bordered w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            autoFocus
          />
        </div>

        {/* コンテンツ */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {/* クイックアクション */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-base-content/70 mb-3">クイックアクション</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {filteredItems.map((item) => (
                <div
                  key={item.id}
                  className="relative group"
                  onMouseEnter={() => setHoveredApp(item.id)}
                  onMouseLeave={() => setHoveredApp(null)}
                >
                  <button
                    onClick={(e) => {
                      // Cmd/Ctrl + クリックでプロジェクト編集
                      if (e.metaKey || e.ctrlKey) {
                        if (item.url) {
                          editProject(item);
                        } else {
                          handleAction(item.action);
                        }
                      } else {
                        // 通常クリックでアプリ起動またはアクション実行
                        if (item.url) {
                          launchApp(item);
                        } else {
                          handleAction(item.action);
                        }
                      }
                    }}
                    className="w-full flex items-center gap-4 p-4 rounded-lg bg-base-200/50 hover:bg-base-200 transition-all neo-hover text-left relative"
                  >
                    {/* アプリ実行状態インジケーター */}
                    {runningApps.has(item.id) && (
                      <div className="absolute top-2 right-2 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    )}
                    
                    <div className={`p-3 rounded-lg border-2 transition-all ${item.bgColor || 'bg-base-300/50'} ${item.borderColor || 'border-transparent'} ${item.color} hover:${item.borderColor?.replace('/30', '/50') || 'border-base-content/20'}`}>
                      <item.icon size={24} />
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold">{item.name}</div>
                      <div className="text-sm text-base-content/60">{item.description}</div>
                      {item.url && (
                        <div className="text-xs text-primary/60 mt-1">クリック:起動 / Cmd+クリック:編集</div>
                      )}
                    </div>
                    
                    {/* 進捗バーとハンマーアイコン */}
                    <div className="flex items-center gap-2">
                      {item.shortcut && (
                        <div className="text-xs text-base-content/40">{item.shortcut}</div>
                      )}
                      {item.url && (
                        <>
                          {/* 進捗ゲージ（仮想的な値） */}
                          <progress 
                            className="progress progress-primary w-12 h-2" 
                            value={runningApps.has(item.id) ? 100 : Math.random() * 100} 
                            max="100"
                          ></progress>
                          {/* ハンマーアイコン */}
                          <div
                            onClick={(e) => {
                              e.stopPropagation();
                              editProject(item);
                            }}
                            className="btn btn-xs btn-ghost btn-circle opacity-60 hover:opacity-100 transition-opacity cursor-pointer"
                            title="プロジェクト編集"
                          >
                            <Hammer size={12} />
                          </div>
                        </>
                      )}
                    </div>
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* 最近のプロジェクト */}
          <div>
            <h3 className="text-sm font-semibold text-base-content/70 mb-3">最近のプロジェクト</h3>
            <div className="space-y-2">
              {recentProjects.map((project) => (
                <button
                  key={project.name}
                  onClick={() => router.push('/projects')}
                  className="w-full flex items-center gap-3 p-3 rounded-lg bg-base-200/30 hover:bg-base-200/50 transition-all neo-hover"
                >
                  <div className="flex-1 text-left">
                    <div className="font-medium">{project.name}</div>
                    <div className="text-xs text-base-content/60 flex items-center gap-2">
                      <Clock size={12} />
                      {project.lastOpened}
                    </div>
                  </div>
                  <div className="text-sm">
                    <progress className="progress progress-primary w-20" value={project.progress} max="100"></progress>
                  </div>
                  <ChevronRight size={16} className="text-base-content/40" />
                </button>
              ))}
            </div>
          </div>

          {/* 最近のコマンド */}
          <div>
            <h3 className="text-sm font-semibold text-base-content/70 mb-3">最近のコマンド</h3>
            <div className="space-y-2">
              {recentCommands.map((cmd, index) => (
                <button
                  key={index}
                  onClick={() => {
                    // ターミナルにフォーカスしてコマンドを入力
                    const terminal = document.querySelector('.zellij-terminal input') as HTMLInputElement;
                    if (terminal) {
                      terminal.value = cmd;
                      terminal.focus();
                    }
                    onClose();
                  }}
                  className="w-full flex items-center gap-3 p-3 rounded-lg bg-base-200/30 hover:bg-base-200/50 transition-all neo-hover text-left"
                >
                  <Code size={16} className="text-accent flex-shrink-0" />
                  <code className="text-sm font-mono flex-1 truncate">{cmd}</code>
                  <ChevronRight size={16} className="text-base-content/40" />
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* フッター */}
        <div className="p-4 border-t border-base-content/10 bg-base-200/30">
          <div className="flex items-center justify-between text-xs text-base-content/60">
            <div>Tip: Cmd+K でクイックランチャーを開く</div>
            <div className="flex items-center gap-2">
              <Star size={12} />
              コマンド・検索・アクション実行
            </div>
          </div>
        </div>
      </div>

      {/* テンプレートモーダル */}
      <ProjectTemplateModal
        isOpen={showTemplateModal}
        onClose={() => setShowTemplateModal(false)}
        onCreateProject={(template, projectName) => {
          console.log('Creating project:', projectName, template);
          // 親コンポーネントのプロジェクト作成ハンドラを呼び出し
          onCreateProject?.(template, projectName);
          setShowTemplateModal(false);
          onClose();
        }}
      />

      {/* Projextウィザード */}
      <ProjectWizard
        isOpen={showProjectWizard}
        onClose={() => setShowProjectWizard(false)}
        onComplete={(projextData) => {
          console.log('Creating Projext:', projextData);
          // TODO: Projext作成処理を実装
          setShowProjectWizard(false);
          onClose();
        }}
      />
    </div>
  );
}