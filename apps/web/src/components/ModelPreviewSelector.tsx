// プレビュー形式のモデル選択UIコンポーネント
'use client';

import React, { useState, useRef } from 'react';
import { Upload, X, Eye, Download, Trash2, Info } from 'lucide-react';
import { useCharacter } from '@/contexts/CharacterContext';

interface ModelPreviewItem {
  id: string;
  name: string;
  type: 'VRM' | 'LIVE2D' | 'GLTF' | 'GLB';
  thumbnail?: string;
  fileSize?: number;
  uploadDate?: string;
  isActive: boolean;
}

interface ModelPreviewSelectorProps {
  agentId: string;
  onModelSelect?: (modelId: string | null) => void;
  className?: string;
}

export default function ModelPreviewSelector({ 
  agentId, 
  onModelSelect,
  className = '' 
}: ModelPreviewSelectorProps) {
  const { 
    getCurrentAgentVRM, 
    uploadVRMModelForAgent, 
    deleteAgentVRMModel,
    characterState 
  } = useCharacter();

  const [isUploading, setIsUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const currentModel = getCurrentAgentVRM();
  const agentSettings = characterState.agentVRMSettings[agentId];

  // モデルプレビューアイテムを生成
  const getModelPreviewItems = (): ModelPreviewItem[] => {
    const items: ModelPreviewItem[] = [];
    
    // 現在のモデル
    if (currentModel) {
      items.push({
        id: currentModel.id,
        name: currentModel.name,
        type: currentModel.type,
        fileSize: currentModel.arrayBuffer?.byteLength,
        isActive: true
      });
    }

    // デフォルトモデル（プレースホルダー）
    if (!currentModel) {
      items.push({
        id: 'default',
        name: 'デフォルトモデル',
        type: 'VRM',
        isActive: false
      });
    }

    return items;
  };

  const modelItems = getModelPreviewItems();

  // ファイルアップロード処理
  const handleFileUpload = async (file: File) => {
    if (!file.name.toLowerCase().endsWith('.vrm')) {
      alert('VRMファイルのみサポートしています');
      return;
    }

    setIsUploading(true);
    try {
      await uploadVRMModelForAgent(agentId, file);
      console.log(`✅ モデルアップロード完了: ${file.name}`);
      onModelSelect?.(currentModel?.id || null);
    } catch (error) {
      console.error('❌ モデルアップロード失敗:', error);
      alert('モデルのアップロードに失敗しました');
    } finally {
      setIsUploading(false);
    }
  };

  // ドラッグ&ドロップ処理
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const vrmFile = files.find(file => file.name.toLowerCase().endsWith('.vrm'));
    
    if (vrmFile) {
      handleFileUpload(vrmFile);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  // ファイル選択処理
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // モデル削除処理
  const handleDeleteModel = async (modelId: string) => {
    if (confirm('このモデルを削除しますか？')) {
      try {
        await deleteAgentVRMModel(agentId);
        onModelSelect?.(null);
      } catch (error) {
        console.error('❌ モデル削除失敗:', error);
        alert('モデルの削除に失敗しました');
      }
    }
  };

  // ファイルサイズをフォーマット
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '不明';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)}MB`;
  };

  // サムネイル生成（将来的にVRMプレビューを実装）
  const generateThumbnail = (model: ModelPreviewItem): string => {
    // 現在はプレースホルダー、将来的にVRMのスクリーンショットを生成
    const colors = {
      VRM: 'bg-gradient-to-br from-blue-400 to-blue-600',
      LIVE2D: 'bg-gradient-to-br from-pink-400 to-pink-600',
      GLTF: 'bg-gradient-to-br from-green-400 to-green-600',
      GLB: 'bg-gradient-to-br from-purple-400 to-purple-600'
    };
    return colors[model.type] || colors.VRM;
  };

  return (
    <div className={`model-preview-selector ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">🎭 モデル選択</h3>
        <p className="text-sm text-base-content/70">
          エージェント「{agentId}」のVRMモデルを管理
        </p>
      </div>

      {/* アップロードエリア */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 mb-4 transition-colors ${
          dragOver 
            ? 'border-primary bg-primary/10' 
            : 'border-base-content/20 hover:border-primary/50'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="text-center">
          <Upload className="mx-auto mb-2 text-base-content/50" size={32} />
          <p className="text-sm font-medium mb-1">
            VRMファイルをドラッグ&ドロップ
          </p>
          <p className="text-xs text-base-content/50 mb-3">
            または
          </p>
          <button
            className="btn btn-outline btn-sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            {isUploading ? '🔄 アップロード中...' : '📁 ファイルを選択'}
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept=".vrm"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>
      </div>

      {/* モデルプレビューグリッド */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {modelItems.map((model) => (
          <div
            key={model.id}
            className={`relative group border rounded-lg overflow-hidden transition-all cursor-pointer ${
              model.isActive 
                ? 'border-primary ring-2 ring-primary/20' 
                : 'border-base-content/20 hover:border-primary/50'
            }`}
            onClick={() => {
              setSelectedModel(model.id);
              onModelSelect?.(model.id === 'default' ? null : model.id);
            }}
          >
            {/* サムネイル */}
            <div className={`aspect-square ${generateThumbnail(model)} flex items-center justify-center`}>
              <div className="text-white text-center">
                <div className="text-2xl mb-1">🎭</div>
                <div className="text-xs font-medium">{model.type}</div>
              </div>
            </div>

            {/* モデル情報 */}
            <div className="p-3">
              <h4 className="font-medium text-sm truncate mb-1">
                {model.name}
              </h4>
              <div className="flex items-center justify-between text-xs text-base-content/60">
                <span>{formatFileSize(model.fileSize)}</span>
                {model.isActive && (
                  <span className="badge badge-primary badge-xs">使用中</span>
                )}
              </div>
            </div>

            {/* アクションボタン */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="flex gap-1">
                <button
                  className="btn btn-xs btn-circle bg-base-100/80 hover:bg-base-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDetails(showDetails === model.id ? null : model.id);
                  }}
                  title="詳細情報"
                >
                  <Info size={12} />
                </button>
                {model.id !== 'default' && (
                  <button
                    className="btn btn-xs btn-circle bg-error/80 hover:bg-error text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteModel(model.id);
                    }}
                    title="削除"
                  >
                    <Trash2 size={12} />
                  </button>
                )}
              </div>
            </div>

            {/* 詳細情報パネル */}
            {showDetails === model.id && (
              <div className="absolute inset-0 bg-base-100/95 backdrop-blur-sm p-3 flex flex-col justify-center">
                <div className="text-xs space-y-1">
                  <div><strong>名前:</strong> {model.name}</div>
                  <div><strong>タイプ:</strong> {model.type}</div>
                  <div><strong>サイズ:</strong> {formatFileSize(model.fileSize)}</div>
                  {model.uploadDate && (
                    <div><strong>アップロード:</strong> {model.uploadDate}</div>
                  )}
                </div>
                <button
                  className="btn btn-xs mt-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDetails(null);
                  }}
                >
                  閉じる
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 統計情報 */}
      <div className="mt-4 p-3 bg-base-200/50 rounded-lg">
        <div className="text-xs text-base-content/70">
          <div className="flex justify-between">
            <span>モデル数:</span>
            <span>{modelItems.filter(m => m.id !== 'default').length}個</span>
          </div>
          <div className="flex justify-between">
            <span>使用容量:</span>
            <span>
              {formatFileSize(
                modelItems.reduce((sum, m) => sum + (m.fileSize || 0), 0)
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
