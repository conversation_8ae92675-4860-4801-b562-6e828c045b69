'use client';

import React, { useState, useRef } from 'react';
import { Upload, Eye, EyeOff, Volume2, VolumeX, ChevronDown, ChevronUp, Wrench } from 'lucide-react';
import { useCharacter } from '../contexts/CharacterContext';

interface CharacterModel {
  id: string;
  name: string;
  type: 'VRM' | 'FBX' | 'LIVE2D' | 'GLB';
  url: string;
  isLoaded: boolean;
}

export default function AICharacterPanel() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [settingsExpanded, setSettingsExpanded] = useState(false);
  
  const { 
    characterState, 
    setCurrentAgent,
    getCurrentAgentVRM,
    setAgentVisibility,
    setMuted,
    setAgentEmotion,
    playAgentAnimation,
    uploadVRMModelForAgent,
    deleteAgentVRMModel
  } = useCharacter();
  
  const currentAgentId = characterState.currentAgentId || 'mother-cto';
  const currentModel = getCurrentAgentVRM();
  const agentSettings = characterState.agentVRMSettings[currentAgentId];
  const isVisible = agentSettings?.isVisible ?? true;
  const isMuted = characterState.globalSettings.isMuted;
  const emotion = agentSettings?.emotion ?? 'neutral';
  const isActive = characterState.globalSettings.isActive;

  const fileInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="border-b border-base-content/10 bg-base-200/40">
      {/* ヘッダー */}
      <div className="p-3 border-b border-base-content/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-accent/20 rounded-full flex items-center justify-center text-xs">
              👤
            </div>
            <h3 className="font-medium text-sm">AIキャラクター</h3>
          </div>
          <div className="flex items-center gap-1">
            {/* VRM更新マーク */}
            <div className="relative">
              <button
                className="btn btn-ghost btn-xs"
                onClick={() => setSettingsExpanded(!settingsExpanded)}
                title="VRM設定"
              >
                <Wrench size={12} />
              </button>
              {/* 更新マーク（新しいモデルやアップデート利用可能時） */}
              {currentModel && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-warning rounded-full border-2 border-base-100 animate-pulse" 
                     title="VRMモデル更新可能">
                  <div className="w-full h-full bg-warning rounded-full animate-ping"></div>
                </div>
              )}
            </div>
            <button
              className={`btn btn-ghost btn-xs ${isMuted ? 'text-error' : 'text-success'}`}
              onClick={() => setMuted(!isMuted)}
            >
              {isMuted ? <VolumeX size={12} /> : <Volume2 size={12} />}
            </button>
            <button
              className={`btn btn-ghost btn-xs ${isVisible ? 'text-primary' : 'text-base-content/40'}`}
              onClick={() => setAgentVisibility(currentAgentId, !isVisible)}
            >
              {isVisible ? <Eye size={12} /> : <EyeOff size={12} />}
            </button>
            <button
              className="btn btn-ghost btn-xs"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? <ChevronDown size={12} /> : <ChevronUp size={12} />}
            </button>
          </div>
        </div>
      </div>

      {/* コンテンツ */}
      {!isCollapsed && (
        <div className="p-3">
          {/* キャラクター表示エリア */}
          <div className="mb-4">
            <div className="aspect-square bg-gradient-to-br from-base-300/30 to-base-300/60 rounded-lg border border-base-content/10 flex items-center justify-center relative overflow-hidden">
              {currentModel && isVisible ? (
                <div className="text-center">
                  <div className={`text-6xl mb-3 ${isActive ? 'animate-bounce' : 'animate-pulse'}`}>
                    🧑
                  </div>
                  <div className="text-sm font-medium">{currentModel.name}</div>
                  <div className="text-xs text-base-content/60">{currentModel.type}</div>
                  
                  {/* 感情・状態表示 */}
                  <div className="mt-2 flex flex-col gap-1">
                    <div className="text-xs text-base-content/50">
                      {emotion === 'speaking' && '🗣️ 発話中'}
                      {emotion === 'listening' && '👂 聞いています'}
                      {emotion === 'thinking' && '🤔 考え中'}
                      {emotion === 'happy' && '😊 喜んでいます'}
                      {emotion === 'neutral' && '😐 中立'}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-base-content/40">
                  <div className="text-4xl mb-3 opacity-50">👤</div>
                  <div className="text-sm">キャラクター未選択</div>
                </div>
              )}
            </div>
          </div>

          {/* 設定 */}
          <div className="space-y-2">
            <button 
              className="btn btn-xs btn-outline w-full flex items-center justify-between"
              onClick={() => setSettingsExpanded(!settingsExpanded)}
            >
              <div className="flex items-center gap-1">
                <Wrench size={10} />
                キャラクター設定
              </div>
              {settingsExpanded ? <ChevronUp size={10} /> : <ChevronDown size={10} />}
            </button>
            
            {settingsExpanded && (
              <div className="bg-base-200/50 rounded-lg p-2 space-y-2">
                {/* VRMモデルアップロード */}
                <div className="border-b border-base-content/10 pb-2">
                  <div className="text-xs font-medium mb-2">VRMモデル管理</div>
                  <button 
                    className="btn btn-xs btn-primary w-full mb-2"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload size={10} />
                    VRMモデルをアップロード
                  </button>
                  
                  {/* モデル一覧 */}
                  <div className="mb-2">
                    <div className="text-xs text-base-content/60 mb-1">読み込み済みモデル</div>
                    <div className="bg-base-300/30 rounded p-2 text-xs">
                      {currentModel ? (
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="font-medium">{currentModel.name}</div>
                            <div className="text-xs text-success">✓ 読み込み済み</div>
                          </div>
                          <button 
                            className="btn btn-xs btn-error btn-outline ml-2"
                            onClick={() => {
                              if (confirm(`VRMモデル「${currentModel.name}」を削除しますか？`)) {
                                const modelName = currentModel.name;
                                deleteAgentVRMModel(currentAgentId);
                                
                                // 削除通知
                                const notification = document.createElement('div');
                                notification.className = 'fixed bottom-4 left-4 z-50 bg-error text-error-content px-4 py-2 rounded-lg shadow-lg flex items-center gap-2';
                                notification.innerHTML = `
                                  <svg viewBox="0 0 180 180" fill="currentColor" class="w-5 h-5">
                                    <path d="M0 0h180v180H0z"/>
                                    <path fill="white" d="M37.44 37.44h88.32v71.28H60.48V60.48h42.24v71.28H37.44z"/>
                                  </svg>
                                  VRMモデル「${modelName}」を削除しました
                                `;
                                document.body.appendChild(notification);
                                setTimeout(() => notification.remove(), 3000);
                              }
                            }}
                            title="モデルを削除"
                          >
                            🗑️
                          </button>
                        </div>
                      ) : (
                        <div className="text-base-content/40 text-center">モデルなし</div>
                      )}
                    </div>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".vrm,.vrma,.gltf,.glb"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        console.log('VRMファイルが選択されました:', file.name);
                        
                        // 既存モデルがある場合は警告
                        if (currentModel) {
                          if (!confirm(`既存のVRMモデル「${currentModel.name}」を新しいモデル「${file.name}」で置き換えますか？`)) {
                            // ファイル選択をリセット
                            e.target.value = '';
                            return;
                          }
                        }
                        
                        // CharacterContextを使ってVRMモデルをアップロード
                        uploadVRMModelForAgent(currentAgentId, file);
                        
                        // VRMアップロード成功の通知
                        const notification = document.createElement('div');
                        notification.className = 'fixed bottom-4 left-4 z-50 bg-success text-success-content px-4 py-2 rounded-lg shadow-lg flex items-center gap-2';
                        notification.innerHTML = `
                          <svg viewBox="0 0 180 180" fill="currentColor" class="w-5 h-5">
                            <path d="M0 0h180v180H0z"/>
                            <path fill="white" d="M37.44 37.44h88.32v71.28H60.48V60.48h42.24v71.28H37.44z"/>
                          </svg>
                          VRMファイル「${file.name}」をアップロードしました
                        `;
                        document.body.appendChild(notification);
                        setTimeout(() => notification.remove(), 3000);
                        
                        // ファイル選択をリセット
                        e.target.value = '';
                      }
                    }}
                  />
                  <div className="text-xs text-base-content/50 text-center">
                    対応形式: .vrm, .vrma, .gltf, .glb
                  </div>
                </div>
                
                <div className="text-xs font-medium mb-2">アニメーション・ポーズ制御</div>
                <div className="grid grid-cols-2 gap-1 mb-2">
                  <button 
                    className="btn btn-xs btn-outline"
                    onClick={() => playAgentAnimation(currentAgentId, 'speak')}
                  >
                    🗣️ 話す
                  </button>
                  <button 
                    className="btn btn-xs btn-outline"
                    onClick={() => playAgentAnimation(currentAgentId, 'listen')}
                  >
                    👂 聞く
                  </button>
                  <button 
                    className="btn btn-xs btn-outline"
                    onClick={() => playAgentAnimation(currentAgentId, 'think')}
                  >
                    🤔 考える
                  </button>
                  <button 
                    className="btn btn-xs btn-outline"
                    onClick={() => playAgentAnimation(currentAgentId, 'idle')}
                  >
                    😐 待機
                  </button>
                </div>
                
                {/* ポーズリセット */}
                <button 
                  className="btn btn-xs btn-warning w-full"
                  onClick={() => {
                    playAgentAnimation(currentAgentId, 'reset');
                    setAgentEmotion(currentAgentId, 'neutral');
                    // トースト通知
                    const notification = document.createElement('div');
                    notification.className = 'fixed top-4 right-4 z-50 bg-warning text-warning-content px-3 py-2 rounded-lg shadow-lg';
                    notification.textContent = 'ポーズとアニメーションをリセットしました';
                    document.body.appendChild(notification);
                    setTimeout(() => notification.remove(), 2000);
                  }}
                >
                  🔄 ポーズリセット
                </button>
                
                <div className="border-t border-base-content/10 pt-2 mt-2">
                  <div className="text-xs text-base-content/60">
                    感情テスト
                  </div>
                  <div className="grid grid-cols-3 gap-1 mt-1">
                    <button 
                      className="btn btn-xs btn-ghost"
                      onClick={() => setAgentEmotion(currentAgentId, 'happy')}
                    >
                      😊
                    </button>
                    <button 
                      className="btn btn-xs btn-ghost"
                      onClick={() => setAgentEmotion(currentAgentId, 'thinking')}
                    >
                      🤔
                    </button>
                    <button 
                      className="btn btn-xs btn-ghost"
                      onClick={() => setAgentEmotion(currentAgentId, 'neutral')}
                    >
                      😐
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

        </div>
      )}
    </div>
  );
}