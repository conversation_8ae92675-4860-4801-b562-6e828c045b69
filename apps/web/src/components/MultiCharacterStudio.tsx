'use client';

import { useState, useRef } from 'react';
import { Users, Video, Mic, Play, Pause, Square, Settings, Plus, Trash2, VolumeX, Volume2, Camera, User, Scan } from 'lucide-react';
import VRMViewer from './VRMViewer';
import FaceTrackingSystem from './FaceTrackingSystem';

interface Character {
  id: string;
  name: string;
  type: 'AI' | 'Human';
  avatar: string;
  avatarImage?: string; // 顔写真URL
  voice: string;
  personality: string;
  isActive: boolean;
  isMuted: boolean;
  vrmUrl?: string;
  vrmArrayBuffer?: ArrayBuffer;
  faceTrackingEnabled?: boolean;
  // 新しい配置・表示設定
  position: { x: number; y: number; z: number };
  scale: number;
  rotation: { x: number; y: number; z: number };
  viewMode: 'bust' | 'full' | 'close';
  isDragging?: boolean;
}

interface FaceTrackingData {
  happy: number;
  angry: number;
  sad: number;
  relaxed: number;
  surprised: number;
  aa: number;
  ih: number;
  ou: number;
  ee: number;
  oh: number;
  eyeBlinkLeft: number;
  eyeBlinkRight: number;
  headRotationX: number;
  headRotationY: number;
  headRotationZ: number;
}

interface RecordingSession {
  id: string;
  title: string;
  duration: number;
  participants: string[];
  status: 'idle' | 'recording' | 'paused' | 'completed';
  createdAt: Date;
  videoUrl?: string;
}

export default function MultiCharacterStudio() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [characters, setCharacters] = useState<Character[]>([
    {
      id: 'ai-1',
      name: 'AITuber - アイ',
      type: 'AI',
      avatar: '🤖',
      avatarImage: '/avatars/ai-girl-1.jpg', // 顔写真（実際のファイルは後で追加）
      voice: 'female',
      personality: '知的で親しみやすいAIアシスタント。最新技術について詳しく説明できる',
      isActive: true,
      isMuted: false,
      faceTrackingEnabled: false,
      position: { x: -2, y: 0, z: 0 }, // 左側配置
      scale: 1.0,
      rotation: { x: 0, y: 0.2, z: 0 }, // 少し右向き
      viewMode: 'bust'
    },
    {
      id: 'human-1',
      name: 'VTuber - あなた',
      type: 'Human',
      avatar: '👨‍💼',
      avatarImage: '/avatars/human-male-1.jpg', // 顔写真（実際のファイルは後で追加）
      voice: 'male',
      personality: 'エネルギッシュなVTuber。視聴者との交流を大切にする',
      isActive: true,
      isMuted: false,
      faceTrackingEnabled: true,
      position: { x: 2, y: 0, z: 0 }, // 右側配置
      scale: 1.0,
      rotation: { x: 0, y: -0.2, z: 0 }, // 少し左向き
      viewMode: 'bust'
    }
  ]);

  // フェイストラッキング関連の状態
  const [faceTrackingData, setFaceTrackingData] = useState<FaceTrackingData | null>(null);
  const [selectedTrackingCharacter, setSelectedTrackingCharacter] = useState<string>('human-1');
  const [vtubeStudioMode, setVtubeStudioMode] = useState(false);

  const [currentSession, setCurrentSession] = useState<RecordingSession | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [sessions, setSessions] = useState<RecordingSession[]>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const addCharacter = () => {
    const newCharacter: Character = {
      id: `char-${Date.now()}`,
      name: `キャラクター${characters.length + 1}`,
      type: 'AI',
      avatar: '🎭',
      voice: 'neutral',
      personality: 'カスタム',
      isActive: false,
      isMuted: false,
      position: { x: 0, y: 0, z: 0 }, // 中央配置
      scale: 1.0,
      rotation: { x: 0, y: 0, z: 0 },
      viewMode: 'bust'
    };
    setCharacters(prev => [...prev, newCharacter]);
  };

  const removeCharacter = (id: string) => {
    setCharacters(prev => prev.filter(char => char.id !== id));
  };

  const toggleCharacterActive = (id: string) => {
    setCharacters(prev => prev.map(char => 
      char.id === id ? { ...char, isActive: !char.isActive } : char
    ));
  };

  const toggleCharacterMute = (id: string) => {
    setCharacters(prev => prev.map(char => 
      char.id === id ? { ...char, isMuted: !char.isMuted } : char
    ));
  };

  const toggleFaceTracking = (id: string) => {
    setCharacters(prev => prev.map(char =>
      char.id === id ? { ...char, faceTrackingEnabled: !char.faceTrackingEnabled } : char
    ));

    // フェイストラッキング対象キャラクターの更新
    const character = characters.find(c => c.id === id);
    if (character?.faceTrackingEnabled) {
      setSelectedTrackingCharacter(id);
    }
  };

  // 新しいキャラクター操作関数
  const updateCharacterPosition = (id: string, position: { x: number; y: number; z: number }) => {
    setCharacters(prev => prev.map(char =>
      char.id === id ? { ...char, position } : char
    ));
  };

  const updateCharacterScale = (id: string, scale: number) => {
    setCharacters(prev => prev.map(char =>
      char.id === id ? { ...char, scale: Math.max(0.5, Math.min(2.0, scale)) } : char
    ));
  };

  const updateCharacterRotation = (id: string, rotation: { x: number; y: number; z: number }) => {
    setCharacters(prev => prev.map(char =>
      char.id === id ? { ...char, rotation } : char
    ));
  };

  const updateCharacterViewMode = (id: string, viewMode: 'bust' | 'full' | 'close') => {
    setCharacters(prev => prev.map(char =>
      char.id === id ? { ...char, viewMode } : char
    ));
  };

  const resetCharacterPosition = (id: string) => {
    const character = characters.find(c => c.id === id);
    if (character) {
      const index = characters.findIndex(c => c.id === id);
      const defaultX = index === 0 ? -2 : index === 1 ? 2 : 0;
      updateCharacterPosition(id, { x: defaultX, y: 0, z: 0 });
      updateCharacterScale(id, 1.0);
      updateCharacterRotation(id, { x: 0, y: index === 0 ? 0.2 : index === 1 ? -0.2 : 0, z: 0 });
    }
  };

  const handleFaceTrackingData = (data: FaceTrackingData) => {
    setFaceTrackingData(data);
    
    // 選択されたキャラクターのVRMに適用
    const targetCharacter = characters.find(c => c.id === selectedTrackingCharacter);
    if (targetCharacter?.faceTrackingEnabled) {
      // VRMViewerにフェイストラッキングデータを送信
      // この部分は後でVRMViewerコンポーネントに実装
      console.log(`フェイストラッキングデータ送信 -> ${targetCharacter.name}:`, data);
    }
  };

  const startRecording = () => {
    const activeChars = characters.filter(char => char.isActive);
    if (activeChars.length < 2) {
      alert('対談には最低2人のキャラクターが必要です');
      return;
    }

    const newSession: RecordingSession = {
      id: Date.now().toString(),
      title: `対談 ${new Date().toLocaleString()}`,
      duration: 0,
      participants: activeChars.map(char => char.name),
      status: 'recording',
      createdAt: new Date()
    };

    setCurrentSession(newSession);
    setIsRecording(true);
    setRecordingTime(0);

    intervalRef.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
    }, 1000);
  };

  const pauseRecording = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setIsRecording(false);
    if (currentSession) {
      setCurrentSession({
        ...currentSession,
        status: 'paused'
      });
    }
  };

  const stopRecording = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setIsRecording(false);
    
    if (currentSession) {
      const completedSession = {
        ...currentSession,
        duration: recordingTime,
        status: 'completed' as const,
        videoUrl: `recording-${currentSession.id}.mp4`
      };
      setSessions(prev => [completedSession, ...prev]);
      setCurrentSession(null);
    }
    setRecordingTime(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="border-b border-base-content/10 bg-base-200/40">
      {/* ヘッダー */}
      <div className="p-3 border-b border-base-content/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-secondary/20 rounded-full flex items-center justify-center text-xs">
              🎬
            </div>
            <h3 className="font-medium text-sm">マルチキャラクター対談スタジオ</h3>
          </div>
          <div className="flex items-center gap-1">
            {isRecording && (
              <div className="flex items-center gap-1 text-error">
                <div className="w-2 h-2 bg-error rounded-full animate-pulse"></div>
                <span className="text-xs font-mono">{formatTime(recordingTime)}</span>
              </div>
            )}
            <button
              className="btn btn-ghost btn-xs"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? '▼' : '▲'}
            </button>
          </div>
        </div>
      </div>

      {/* コンテンツ */}
      {!isCollapsed && (
        <div className="p-3 space-y-3">
          {/* キャラクター管理 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium">参加キャラクター</span>
              <button className="btn btn-xs btn-outline" onClick={addCharacter}>
                <Plus size={8} />
                追加
              </button>
            </div>

            {/* 新しい顔写真アイコンベースの選択UI */}
            <div className="flex flex-wrap gap-3 justify-center">
              {characters.map((character) => (
                <div
                  key={character.id}
                  className={`relative group cursor-pointer transition-all duration-300 ${
                    character.isActive
                      ? 'scale-110 ring-4 ring-primary ring-opacity-50'
                      : 'hover:scale-105'
                  }`}
                  onClick={() => toggleCharacterActive(character.id)}
                >
                  {/* 顔写真アバター */}
                  <div className={`w-16 h-16 rounded-full overflow-hidden border-4 transition-all ${
                    character.isActive
                      ? 'border-primary shadow-lg'
                      : 'border-base-content/20 group-hover:border-primary/50'
                  }`}>
                    {character.avatarImage ? (
                      <img
                        src={character.avatarImage}
                        alt={character.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // 画像読み込み失敗時はemoji表示
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full flex items-center justify-center text-2xl bg-gradient-to-br ${
                      character.type === 'AI' ? 'from-blue-400 to-purple-500' : 'from-green-400 to-blue-500'
                    } ${character.avatarImage ? 'hidden' : 'flex'}`}>
                      {character.avatar}
                    </div>
                  </div>

                  {/* 参加ステータス表示 */}
                  {character.isActive && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-success rounded-full border-2 border-base-100 animate-pulse flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}

                  {/* キャラクター名とタイプ */}
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                    <div className="text-xs font-medium truncate max-w-20">{character.name.split(' - ')[1] || character.name}</div>
                    <div className={`text-xs px-1 rounded ${
                      character.type === 'AI' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
                    }`}>
                      {character.type}
                    </div>
                  </div>

                  {/* ホバー時の詳細コントロール */}
                  <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex flex-col gap-1">
                      <button
                        className={`btn btn-xs btn-circle ${
                          character.isMuted ? 'btn-error' : 'btn-success'
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCharacterMute(character.id);
                        }}
                        title={character.isMuted ? '音声オン' : '音声オフ'}
                      >
                        {character.isMuted ? <VolumeX size={8} /> : <Volume2 size={8} />}
                      </button>

                      {character.type === 'Human' && (
                        <button
                          className={`btn btn-xs btn-circle ${
                            character.faceTrackingEnabled ? 'btn-info' : 'btn-outline'
                          }`}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleFaceTracking(character.id);
                          }}
                          title={character.faceTrackingEnabled ? 'フェイストラッキング停止' : 'フェイストラッキング開始'}
                        >
                          <Scan size={8} />
                        </button>
                      )}
                    </div>
                </div>
              ))}
            </div>

            {/* キャラクター追加・削除コントロール */}
            <div className="flex justify-center gap-2 mt-4">
              <button
                className="btn btn-sm btn-outline"
                onClick={addCharacter}
                title="新しいキャラクターを追加"
              >
                <Plus size={16} />
                キャラクター追加
              </button>
              {characters.length > 2 && (
                <button
                  className="btn btn-sm btn-error btn-outline"
                  onClick={() => {
                    const inactiveChar = characters.find(c => !c.isActive);
                    if (inactiveChar) {
                      removeCharacter(inactiveChar.id);
                    }
                  }}
                  title="非参加キャラクターを削除"
                >
                  <Trash2 size={16} />
                  削除
                </button>
              )}
            </div>
          </div>

          {/* フェイストラッキングシステム */}
          {characters.some(char => char.faceTrackingEnabled) && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium">フェイストラッキング</span>
                <div className="flex items-center gap-1">
                  <select
                    className="select select-xs select-bordered"
                    value={selectedTrackingCharacter}
                    onChange={(e) => setSelectedTrackingCharacter(e.target.value)}
                  >
                    {characters
                      .filter(char => char.type === 'Human' && char.faceTrackingEnabled)
                      .map(char => (
                        <option key={char.id} value={char.id}>
                          {char.name}
                        </option>
                      ))
                    }
                  </select>
                  <button
                    className={`btn btn-xs ${vtubeStudioMode ? 'btn-primary' : 'btn-outline'}`}
                    onClick={() => setVtubeStudioMode(!vtubeStudioMode)}
                    title="VTube Studio統合"
                  >
                    VTS
                  </button>
                </div>
              </div>
              
              <FaceTrackingSystem
                onTrackingData={handleFaceTrackingData}
                vtubeStudioMode={vtubeStudioMode}
                targetCharacterId={selectedTrackingCharacter}
                className="max-w-sm"
              />
            </div>
          )}

          {/* 録画コントロール */}
          <div className="bg-base-100 rounded-lg p-3 space-y-2">
            <div className="text-xs font-medium">録画コントロール</div>
            
            {currentSession ? (
              <div className="space-y-2">
                <div className="text-xs">
                  <div className="font-medium">{currentSession.title}</div>
                  <div className="text-base-content/60">
                    参加者: {currentSession.participants.join(', ')}
                  </div>
                </div>
                <div className="flex gap-1 justify-center">
                  {isRecording ? (
                    <>
                      <button className="btn btn-xs btn-warning flex-1" onClick={pauseRecording}>
                        <Pause size={8} />
                        一時停止
                      </button>
                      <button className="btn btn-xs btn-error flex-1" onClick={stopRecording}>
                        <Square size={8} />
                        停止
                      </button>
                    </>
                  ) : (
                    <>
                      <button className="btn btn-xs btn-success flex-1" onClick={() => setIsRecording(true)}>
                        <Play size={8} />
                        再開
                      </button>
                      <button className="btn btn-xs btn-error flex-1" onClick={stopRecording}>
                        <Square size={8} />
                        終了
                      </button>
                    </>
                  )}
                </div>
              </div>
            ) : (
              <button 
                className="btn btn-xs btn-primary"
                onClick={startRecording}
                disabled={characters.filter(char => char.isActive).length < 2}
              >
                <Video size={8} />
                録画開始
              </button>
            )}
          </div>

          {/* 新しいシームレス対談スタジオ - 3D空間表示 */}
          <div className="bg-gradient-to-b from-slate-900 to-slate-800 rounded-lg aspect-video relative overflow-hidden border border-base-content/20">
            {currentSession ? (
              <div className="w-full h-full relative">
                {/* 録画ステータス */}
                {isRecording && (
                  <div className="absolute top-4 right-4 z-20 flex items-center gap-2 bg-error/90 backdrop-blur-sm rounded-lg px-3 py-2">
                    <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                    <span className="text-sm font-mono text-white font-bold">{formatTime(recordingTime)}</span>
                  </div>
                )}

                {/* 3D空間シームレス表示エリア */}
                <div className="w-full h-full relative">
                  {characters.filter(char => char.isActive).map((character, index) => (
                    <div
                      key={character.id}
                      className="absolute inset-0"
                      style={{
                        transform: `translate3d(${character.position.x * 50}px, ${character.position.y * 50}px, 0) scale(${character.scale})`,
                        transformOrigin: 'center center',
                        zIndex: character.position.z + 10
                      }}
                    >
                      {/* キャラクター名とタイプ表示 */}
                      <div className="absolute bottom-4 left-4 z-10 bg-black/60 backdrop-blur-sm rounded-lg px-3 py-2">
                        <div className="text-sm font-bold text-white">{character.name}</div>
                        <div className={`text-xs font-medium ${
                          character.type === 'AI' ? 'text-blue-300' : 'text-green-300'
                        }`}>
                          {character.type === 'AI' ? '🤖 AITuber' : '🎭 VTuber'}
                        </div>
                      </div>

                      {/* キャラクター操作パネル */}
                      <div className="absolute top-4 left-4 z-10 bg-black/40 backdrop-blur-sm rounded-lg p-2 opacity-0 hover:opacity-100 transition-opacity">
                        <div className="flex flex-col gap-1">
                          {/* ビューモード切り替え */}
                          <div className="flex gap-1">
                            {['close', 'bust', 'full'].map((mode) => (
                              <button
                                key={mode}
                                className={`btn btn-xs ${character.viewMode === mode ? 'btn-primary' : 'btn-ghost'}`}
                                onClick={() => updateCharacterViewMode(character.id, mode as any)}
                                title={mode === 'close' ? 'クローズアップ' : mode === 'bust' ? 'バストアップ' : '全身'}
                              >
                                {mode === 'close' ? '👤' : mode === 'bust' ? '👥' : '🔍'}
                              </button>
                            ))}
                          </div>

                          {/* スケール調整 */}
                          <div className="flex items-center gap-1">
                            <button
                              className="btn btn-xs btn-ghost"
                              onClick={() => updateCharacterScale(character.id, character.scale - 0.1)}
                              title="縮小"
                            >
                              ➖
                            </button>
                            <span className="text-xs text-white px-1">{(character.scale * 100).toFixed(0)}%</span>
                            <button
                              className="btn btn-xs btn-ghost"
                              onClick={() => updateCharacterScale(character.id, character.scale + 0.1)}
                              title="拡大"
                            >
                              ➕
                            </button>
                          </div>

                          {/* 位置リセット */}
                          <button
                            className="btn btn-xs btn-ghost"
                            onClick={() => resetCharacterPosition(character.id)}
                            title="位置リセット"
                          >
                            🔄
                          </button>
                        </div>
                      </div>
                      
                      {/* 高度なVRMビューアー - 3D空間対応 */}
                      <VRMViewer
                        vrmUrl={character.vrmUrl}
                        vrmArrayBuffer={character.vrmArrayBuffer}
                        agentId={character.id}
                        isActive={character.isActive && !character.isMuted}
                        className="w-full h-full"
                        showControls={false}
                        viewMode={character.viewMode}
                        // フェイストラッキング
                        faceTrackingData={
                          character.id === selectedTrackingCharacter && character.faceTrackingEnabled
                            ? faceTrackingData
                            : null
                        }
                        // モーション機能
                        motion={character.isActive && !character.isMuted ? 'speaking' : 'idle'}
                        autoIdleMotion={true}
                        onMotionComplete={(motionName) => {
                          console.log(`🎬 モーション完了: ${character.name} - ${motionName}`);
                        }}
                        // VRMアップロード
                        onVRMUpload={async (file) => {
                          try {
                            console.log(`📁 対談スタジオVRMアップロード開始: ${character.name}`, file);

                            // ファイルをArrayBufferに変換
                            const arrayBuffer = await file.arrayBuffer();
                            const url = URL.createObjectURL(file);

                            setCharacters(prev => prev.map(c =>
                              c.id === character.id
                                ? {
                                    ...c,
                                    vrmUrl: url,
                                    vrmArrayBuffer: arrayBuffer
                                  }
                                : c
                            ));

                            console.log(`✅ 対談スタジオVRMアップロード完了: ${character.name} - ${file.name}`);
                          } catch (error) {
                            console.error(`❌ 対談スタジオVRMアップロードエラー: ${character.name}`, error);
                            alert(`VRMファイルの読み込みに失敗しました: ${error.message || 'Unknown error'}`);
                          }
                        }}
                      />
                      
                      {/* マイクミュート表示 */}
                      {character.isMuted && (
                        <div className="absolute top-2 right-2 bg-error/80 rounded p-1">
                          <VolumeX size={12} className="text-white" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                
                {/* 対談情報 */}
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-base-200/90 rounded px-3 py-1">
                  <div className="text-xs font-medium text-center">
                    🎬 {currentSession.participants.join(' × ')} 対談中
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center text-base-content/60">
                <Camera size={32} className="mb-3 opacity-50" />
                <div className="text-sm font-medium">VTUBER × AITUBER 対談スタジオ</div>
                <div className="text-xs mt-1">録画を開始して対談を始めましょう</div>
                <div className="text-xs mt-2 text-base-content/40">
                  💡 キャラクターにVRMモデルを設定すると3D表示されます
                </div>
              </div>
            )}
          </div>

          {/* 録画履歴 */}
          {sessions.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs font-medium">録画履歴</div>
              <div className="max-h-20 overflow-y-auto space-y-1">
                {sessions.slice(0, 3).map((session) => (
                  <div key={session.id} className="bg-base-100 rounded p-2">
                    <div className="flex items-center justify-between">
                      <div className="text-xs">
                        <div className="font-medium truncate">{session.title}</div>
                        <div className="text-base-content/60">
                          {formatTime(session.duration)} • {session.participants.length}人
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <button className="btn btn-ghost btn-xs">
                          <Play size={8} />
                        </button>
                        <button className="btn btn-ghost btn-xs">
                          <Settings size={8} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}