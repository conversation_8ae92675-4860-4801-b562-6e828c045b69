'use client';

import { useState } from 'react';
import { Folder, Play, Pause, Settings, Code, BarChart3, Trash2, Plus, Clock, User, CheckCircle } from 'lucide-react';
import ProjectTemplateModal from './ProjectTemplateModal';

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'development' | 'testing' | 'deployed' | 'paused';
  progress: number;
  king: string;
  general: string;
  soldiers: number;
  lastUpdated: Date;
  technologies: string[];
}

export default function ProjectManager() {
  const [projects, setProjects] = useState<Project[]>([
    {
      id: '1',
      name: 'meta-studio',
      description: 'AI Vtuberプラットフォーム・統合開発環境',
      status: 'development',
      progress: 95,
      king: 'K-001',
      general: 'G-metastudio',
      soldiers: 12,
      lastUpdated: new Date(),
      technologies: ['Next.js', 'TypeScript', 'Tailwind CSS', 'Claude API']
    },
    {
      id: '3',
      name: 'iS_streamer_projext', 
      description: 'バーチャル配信者「みけちゃん」システム',
      status: 'planning',
      progress: 25,
      king: 'K-003',
      general: 'G-entertainment',
      soldiers: 3,
      lastUpdated: new Date(),
      technologies: ['Unity', 'Live2D', 'WebRTC', 'AI Voice']
    }
  ]);

  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const getStatusBadge = (status: Project['status']) => {
    const badges = {
      planning: 'badge-warning',
      development: 'badge-primary', 
      testing: 'badge-info',
      deployed: 'badge-success',
      paused: 'badge-neutral'
    };
    const labels = {
      planning: '企画中',
      development: '開発中',
      testing: 'テスト中', 
      deployed: 'デプロイ済',
      paused: '一時停止'
    };
    return { class: badges[status], label: labels[status] };
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'progress-success';
    if (progress >= 50) return 'progress-primary';
    if (progress >= 25) return 'progress-warning';
    return 'progress-error';
  };

  const handleCreateProject = (template: any, projectName: string) => {
    const newProject: Project = {
      id: Date.now().toString(),
      name: projectName,
      description: template.description,
      status: 'planning',
      progress: 0,
      king: template.agentConfig.king,
      general: template.agentConfig.generals[0] || 'General',
      soldiers: template.agentConfig.soldiers.length,
      lastUpdated: new Date(),
      technologies: template.technologies
    };
    
    setProjects(prev => [...prev, newProject]);
    setShowCreateModal(false);
  };

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 neo-metal">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">プロジェクト管理</h1>
            <p className="text-sm text-base-content/70">全{projects.length}プロジェクト</p>
          </div>
          <button 
            className="btn btn-primary neo-hover"
            onClick={() => setShowCreateModal(true)}
          >
            <Plus size={16} />
            新規プロジェクト
          </button>
        </div>

        {/* フィルター */}
        <div className="flex gap-2">
          {(['all', 'planning', 'development', 'testing', 'deployed'] as const).map((filter) => (
            <button
              key={filter}
              className="btn btn-xs btn-outline neo-hover"
            >
              {filter === 'all' ? '全て' : getStatusBadge(filter as Project['status']).label}
            </button>
          ))}
        </div>
      </div>

      {/* プロジェクト一覧 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {projects.map((project) => (
            <div
              key={project.id}
              className={`card bg-base-200 shadow-xl neo-depth cursor-pointer transition-all hover:scale-105 ${
                selectedProject === project.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setSelectedProject(project.id)}
            >
              <div className="card-body p-4">
                {/* プロジェクト名とステータス */}
                <div className="flex items-start justify-between mb-3">
                  <h3 className="card-title text-base">{project.name}</h3>
                  <div className={`badge ${getStatusBadge(project.status).class} badge-sm`}>
                    {getStatusBadge(project.status).label}
                  </div>
                </div>

                {/* 説明 */}
                <p className="text-sm text-base-content/70 mb-3 line-clamp-2">
                  {project.description}
                </p>

                {/* 進捗バー */}
                <div className="mb-3">
                  <div className="flex justify-between text-xs mb-1">
                    <span>進捗</span>
                    <span>{project.progress}%</span>
                  </div>
                  <progress 
                    className={`progress ${getProgressColor(project.progress)} w-full h-2`}
                    value={project.progress} 
                    max="100"
                  ></progress>
                </div>

                {/* エージェント情報 */}
                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center">
                    <div className="text-primary font-bold">{project.king}</div>
                    <div className="text-base-content/60">王</div>
                  </div>
                  <div className="text-center">
                    <div className="text-secondary font-bold">{project.general}</div>
                    <div className="text-base-content/60">将</div>
                  </div>
                  <div className="text-center">
                    <div className="text-accent font-bold">{project.soldiers}</div>
                    <div className="text-base-content/60">兵</div>
                  </div>
                </div>

                {/* 技術スタック */}
                <div className="mt-3">
                  <div className="flex flex-wrap gap-1">
                    {project.technologies.slice(0, 3).map((tech) => (
                      <span key={tech} className="badge badge-outline badge-xs">
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="badge badge-outline badge-xs">
                        +{project.technologies.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* アクション */}
                <div className="card-actions justify-end mt-3">
                  <button className="btn btn-xs btn-outline neo-hover">
                    <Code size={12} />
                  </button>
                  <button className="btn btn-xs btn-outline neo-hover">
                    <BarChart3 size={12} />
                  </button>
                  <button className="btn btn-xs btn-outline neo-hover">
                    <Settings size={12} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 詳細パネル */}
      {selectedProject && (
        <div className="border-t border-base-content/10 p-4 bg-base-200/50">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">プロジェクト詳細</h3>
            <button 
              className="btn btn-xs btn-ghost"
              onClick={() => setSelectedProject(null)}
            >
              ✕
            </button>
          </div>
          <div className="mt-2 text-sm text-base-content/70">
            選択されたプロジェクト: {projects.find(p => p.id === selectedProject)?.name}
          </div>
        </div>
      )}

      {/* テンプレートモーダル */}
      <ProjectTemplateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreateProject={handleCreateProject}
      />
    </div>
  );
}