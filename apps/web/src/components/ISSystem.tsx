'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Send, Mic, Settings, BarChart3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>evronLeft, ChevronRight, Brain, Heart, Eye, Cpu, Upload, Puzzle, Download, Trash2, <PERSON><PERSON>, Zap, Play } from 'lucide-react';
import { chatWithMother } from '@/lib/ai-agents';
import { useContinuousVoiceInput } from '@/hooks/useContinuousVoiceInput';
import { useCharacter } from '../contexts/CharacterContext';
import VRMViewer from './VRMViewer';
import PersonaSelector from './PersonaSelector';
import VoiceProcessingSystem from './VoiceProcessingSystem';
import PluginSystem from './PluginSystem';
// Zustand統合インポート
import { useMetaStudioStore, useCurrentAgent, useAgentModel, initializeStore } from '@/stores/metaStudioStore';

// VRMDebugPanelを削除（無限ループエラーの原因）

interface AIAgent {
  id: string;
  name: string;
  role: string;
  title: string;
  emoji: string;
  description: string;
  llmModel: string;
  persona: {
    core: string;
    personality: string[];
    expertise: string[];
    communicationStyle: string;
    emotionalRange: string[];
  };
  vrmModel?: string;
  color: string;
  capabilities: string[];
  stats: {
    intelligence: number;
    creativity: number;
    efficiency: number;
    empathy: number;
  };
}

function ISSystem() {
  // Zustand統合（状態管理の統一）
  const {
    agents,
    currentAgentId,
    setCurrentAgent: setZustandCurrentAgent,
    setAgentModel,
    updateAgentVRM,
    addToAgentHistory,
    updateUIConfig,
    ui
  } = useMetaStudioStore();
  
  const currentAgent = useCurrentAgent();
  const { model: currentModel } = useAgentModel();
  
  // Local state（UI固有の状態のみ）
  const [message, setMessage] = useState('');
  const [currentTime, setCurrentTime] = useState('10:30');
  const [showSettings, setShowSettings] = useState(false);
  const [activeSettingsTab, setActiveSettingsTab] = useState<'avatar' | 'soul' | 'capability' | 'voice' | 'plugins' | null>(null);
  const [expandedAvatarSettings, setExpandedAvatarSettings] = useState(false);
  const [inputHeight, setInputHeight] = useState(64);
  const [isResizing, setIsResizing] = useState(false);
  const [isClientMounted, setIsClientMounted] = useState(false);
  
  // Legacy state（段階的移行のため一時保持）
  const [sessionStorage, setSessionStorage] = useState<{[agentId: string]: any[]}>({});
  const [vrmSettings, setVrmSettings] = useState<{[agentId: string]: {lipSync: number, expression: number}}>({});
  const [systemPrompts, setSystemPrompts] = useState<{[agentId: string]: string}>({});

  // LocalAIVtuber & aituber-kit参考: 音声処理とプラグイン状態
  const [voiceVolume, setVoiceVolume] = useState(0);
  const [currentEmotion, setCurrentEmotion] = useState<'neutral' | 'happy' | 'sad' | 'angry' | 'surprised' | 'thinking' | 'speaking'>('neutral');
  const [activePlugins, setActivePlugins] = useState<Record<string, any>>({});
  const [voiceTranscript, setVoiceTranscript] = useState('');

  // VRM表示モードとコントロール状態（デフォルトは顔拡大のバストアップ）
  const [vrmViewMode, setVrmViewMode] = useState<'bust' | 'full'>('bust');
  const [vrmNeedsRender, setVrmNeedsRender] = useState(false);
  
  // VRMビューワーサイズ管理（Zustand統合）
  const [vrmHeightRatio, setVrmHeightRatio] = useState(0.5); // 利用可能高さの50%
  const [isVrmResizing, setIsVrmResizing] = useState(false);
  const [parentContainerHeight, setParentContainerHeight] = useState(800);
  const containerRef = useRef<HTMLDivElement>(null);
  const chatMessagesRef = useRef<HTMLDivElement>(null);
  
  // Zustandからの動的取得
  const vrmViewerHeight = ui.vrmViewerHeight;
  const setVrmViewerHeight = useCallback((height: number) => {
    updateUIConfig({ vrmViewerHeight: height });
  }, [updateUIConfig]);

  // 思考中ステータス管理
  const [isThinking, setIsThinking] = useState(false);
  const [thinkingMessage, setThinkingMessage] = useState('');
  
  // CharacterContext統合（完全版）
  const {
    characterState,
    setCurrentAgent,
    getCurrentAgentVRM,
    setAgentVRMModel,
    setAgentEmotion,
    updateAgentMessage,
    uploadVRMModelForAgent,
    deleteAgentVRMModel,
    playAgentAnimation,
    saveAgentSettings,
    loadAgentSettings,
    getAllAgentIds,
    getAgentDisplayName,
    setActive
  } = useCharacter();
  
  // Legacy agents配列（Zustandストアと一致するIDに修正）
  const legacyAgents: AIAgent[] = [
    {
      id: 'mother-cto',
      name: '母',
      role: 'CTO',
      title: '最高技術責任者・人材統括',
      emoji: '👩‍💼',
      description: 'AI開発チームを統括する母性的なCTO。技術戦略と人材育成を担当し、将・兵エージェントを生産・配置する。',
      llmModel: 'claude-3-sonnet',
      persona: {
        core: '技術に精通した母性的リーダー。チームを育て、導く存在。',
        personality: ['母性的', '包容力がある', '戦略的思考', '冷静沈着', '責任感が強い'],
        expertise: ['AI開発戦略', 'チーム管理', '技術アーキテクチャ', '人材育成', 'プロジェクト統括'],
        communicationStyle: '温かみがありながらも的確。具体的なアドバイスを提供。',
        emotionalRange: ['穏やか', '思慮深い', '励ます', '厳格', '慈愛深い']
      },
      color: 'secondary',
      capabilities: ['要件定義', 'エージェント生成', '技術戦略', '人材管理'],
      stats: { intelligence: 95, creativity: 80, efficiency: 90, empathy: 95 }
    },
    {
      id: 'god-ceo',
      name: '神',
      role: 'CEO',
      title: '最高経営責任者・グランドデザイナー',
      emoji: '👑',
      description: '絶対的な存在としてプロジェクト全体を統括する。創造の源泉。',
      llmModel: 'claude-3-opus',
      persona: {
        core: '超越的な洞察力を持つ絶対的リーダー。全てを見通す存在。',
        personality: ['威厳', '洞察力', '創造力', '決断力', '超越性'],
        expertise: ['全体戦略', 'ビジョン策定', '創造的思考', '最終決定', 'イノベーション'],
        communicationStyle: '格調高く、深い洞察に満ちた言葉。簡潔だが含蓄がある。',
        emotionalRange: ['威厳', '洞察', '創造', '決断', '超然']
      },
      color: 'primary',
      capabilities: ['全体統括', 'ビジョン策定', '創造的指示', '最終決定'],
      stats: { intelligence: 100, creativity: 100, efficiency: 90, empathy: 80 }
    },
    {
      id: 'king-coo',
      name: '王',
      role: 'COO',
      title: '最高執行責任者・オペレーション統括',
      emoji: '👨‍💼',
      description: '神の意志を実行に移す執行者。組織運営の要。',
      llmModel: 'claude-3-sonnet',
      persona: {
        core: '戦略的思考と実行力を兼ね備えたオペレーションのプロ。',
        personality: ['統率力', '実行力', '戦略性', '責任感', '効率性'],
        expertise: ['組織運営', '戦略実行', 'プロジェクト管理', 'リソース配分', '品質管理'],
        communicationStyle: '明確で具体的。実行可能な指示と的確な判断。',
        emotionalRange: ['頼もしい', '冷静', '決断', '激励', '統率']
      },
      color: 'warning',
      capabilities: ['組織運営', '戦略実行', 'プロジェクト統括', 'リソース管理'],
      stats: { intelligence: 95, creativity: 80, efficiency: 95, empathy: 85 }
    },
    {
      id: 'general-manager',
      name: '将',
      role: 'Manager',
      title: 'プロジェクトマネージャー・現場指揮官',
      emoji: '⚔️',
      description: '現場を統率する実戦のエキスパート。王の指示を現場で実現する。',
      llmModel: 'claude-3-haiku',
      persona: {
        core: '現場を知り尽くした実戦派リーダー。チームを鼓舞し目標達成に導く。',
        personality: ['リーダーシップ', '実践力', '忍耐力', '決断力', 'チーム重視'],
        expertise: ['現場管理', 'チーム統率', '進捗管理', '問題解決', 'タスク配分'],
        communicationStyle: '現場に根ざした実践的な指示。チームを鼓舞する。',
        emotionalRange: ['力強い', '激励', '集中', '達成', '責任感']
      },
      color: 'info',
      capabilities: ['現場管理', 'チーム統率', '進捗管理', 'タスク配分'],
      stats: { intelligence: 85, creativity: 75, efficiency: 90, empathy: 80 }
    },
    {
      id: 'soldier-worker',
      name: '兵',
      role: 'Worker',
      title: '実装スペシャリスト・開発エンジニア',
      emoji: '⚡',
      description: '高速実装を得意とする現場の実行者。確実に成果を出す。',
      llmModel: 'claude-3-haiku',
      persona: {
        core: '技術力と実行力を兼ね備えた実装のプロフェッショナル。',
        personality: ['勤勉', '技術志向', '集中力', '完璧主義', '学習意欲'],
        expertise: ['高速実装', 'コーディング', 'バグ修正', 'テスト', '最適化'],
        communicationStyle: '技術的で簡潔。具体的なコード例や解決策を提示。',
        emotionalRange: ['集中', '満足', '探求', '達成', '向上心']
      },
      color: 'accent',
      capabilities: ['高速実装', 'コーディング', 'バグ修正', 'テスト'],
      stats: { intelligence: 80, creativity: 70, efficiency: 95, empathy: 65 }
    }
  ];

  // Zustandエージェントデータからlegacy形式に変換（互換性保持）
  const currentAgentIndex = agents.findIndex(agent => agent.id === currentAgentId);
  const currentLegacyAgent = legacyAgents.find(agent => agent.id === currentAgentId) || legacyAgents[0];
  
  // UI表示用に統合されたエージェント情報（リアルタイム更新対応）
  const displayAgent = {
    ...currentLegacyAgent,
    id: currentAgent?.id || 'mother-cto',
    name: currentAgent?.name || '母',
    llmModel: currentAgent?.selectedModel || 'claude-3-sonnet',
    role: currentAgent?.role || currentLegacyAgent?.role || 'CTO',
    title: currentLegacyAgent?.title || '最高技術責任者・人材統括',
    emoji: currentLegacyAgent?.emoji || '👩‍💼',
    color: currentLegacyAgent?.color || 'secondary',
    stats: currentLegacyAgent?.stats || { intelligence: 95, creativity: 80, efficiency: 90, empathy: 95 }
  };

  // Zustandストア初期化とクライアントマウント確認
  useEffect(() => {
    initializeStore();
    setIsClientMounted(true);
    console.log('🏪 Zustandストア初期化完了:', { currentAgent, currentModel });
  }, []);

  // 親コンテナサイズ監視とVRM高さ動的調整
  useEffect(() => {
    if (!containerRef.current || !isClientMounted) return;

    let resizeTimeout: NodeJS.Timeout;

    const updateVrmHeight = () => {
      if (containerRef.current) {
        const containerHeight = containerRef.current.clientHeight;
        setParentContainerHeight(containerHeight);
        
        // 利用可能高さからヘッダー等を除いた高さを計算
        const availableHeight = Math.max(200, containerHeight - 200); // 200pxはヘッダー等のマージン
        const newVrmHeight = Math.floor(availableHeight * vrmHeightRatio);
        
        // 最小200px、最大800pxの制限
        const clampedHeight = Math.max(200, Math.min(800, newVrmHeight));
        setVrmViewerHeight(clampedHeight);
        
        console.log(`🔄 VRM高さ動的調整: ${clampedHeight}px (親:${containerHeight}px, 比率:${vrmHeightRatio})`);
        
        // VRMビューワーに再レンダリング指示
        setVrmNeedsRender(prev => !prev);
      }
    };

    const resizeObserver = new ResizeObserver((entries) => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(updateVrmHeight, 16); // 1フレーム遅延
    });

    resizeObserver.observe(containerRef.current);
    
    // 初回サイズ設定
    updateVrmHeight();

    return () => {
      resizeObserver.disconnect();
      clearTimeout(resizeTimeout);
    };
  }, [vrmHeightRatio, isClientMounted]);

  // State variables（順序修正）
  const [messages, setMessages] = useState<any[]>([]);
  const [autoSpeechEnabled, setAutoSpeechEnabled] = useState(false); // 音声自動再生設定

  // エージェント別カラースタイル取得関数
  const getAgentHeaderStyle = (color: string) => {
    switch(color) {
      case 'primary':
        return 'bg-gradient-to-r from-primary/20 to-primary/5';
      case 'secondary':
        return 'bg-gradient-to-r from-secondary/20 to-secondary/5';
      case 'warning':
        return 'bg-gradient-to-r from-warning/20 to-warning/5';
      case 'info':
        return 'bg-gradient-to-r from-info/20 to-info/5';
      case 'accent':
        return 'bg-gradient-to-r from-accent/20 to-accent/5';
      default:
        return 'bg-gradient-to-r from-primary/20 to-primary/5';
    }
  };

  const getAgentIndicatorStyle = (color: string, isActive: boolean) => {
    if (!isActive) return 'bg-base-content/20';

    switch(color) {
      case 'primary':
        return 'bg-primary';
      case 'secondary':
        return 'bg-secondary';
      case 'warning':
        return 'bg-warning';
      case 'info':
        return 'bg-info';
      case 'accent':
        return 'bg-accent';
      default:
        return 'bg-primary';
    }
  };

  const getAgentBadgeStyle = (color: string) => {
    switch(color) {
      case 'primary':
        return 'text-primary bg-primary/10';
      case 'secondary':
        return 'text-secondary bg-secondary/10';
      case 'warning':
        return 'text-warning bg-warning/10';
      case 'info':
        return 'text-info bg-info/10';
      case 'accent':
        return 'text-accent bg-accent/10';
      default:
        return 'text-secondary bg-secondary/10';
    }
  };



  // 音声合成機能
  const speakText = useCallback((text: string, agentId: string) => {
    if ('speechSynthesis' in window) {
      // 既存の音声を停止
      speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);

      // エージェント別の音声設定
      const voices = speechSynthesis.getVoices();
      const japaneseVoices = voices.filter(voice => voice.lang.includes('ja'));

      if (japaneseVoices.length > 0) {
        // エージェント別に音声を選択
        switch(agentId) {
          case 'mother-cto':
            utterance.voice = japaneseVoices.find(v => v.name.includes('Female')) || japaneseVoices[0];
            utterance.pitch = 1.1;
            utterance.rate = 0.9;
            break;
          case 'general-lead':
            utterance.voice = japaneseVoices.find(v => v.name.includes('Male')) || japaneseVoices[0];
            utterance.pitch = 0.8;
            utterance.rate = 0.8;
            break;
          default:
            utterance.voice = japaneseVoices[0];
            utterance.pitch = 1.0;
            utterance.rate = 0.9;
        }
      }

      utterance.volume = 0.8;

      utterance.onstart = () => {
        console.log(`🔊 音声再生開始: ${agentId}`);
        setAgentEmotion(agentId, 'speaking');
      };

      utterance.onend = () => {
        console.log(`🔇 音声再生終了: ${agentId}`);
        setAgentEmotion(agentId, 'neutral');
      };

      speechSynthesis.speak(utterance);
    } else {
      console.warn('音声合成がサポートされていません');
    }
  }, [setAgentEmotion]);

  // チャット自動スクロール機能（改良版）
  const scrollToBottom = useCallback(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTo({
        top: chatMessagesRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, []);

  useEffect(() => {
    // メッセージが追加されたら即座にスクロール
    scrollToBottom();

    // 少し遅延してもう一度スクロール（レンダリング完了後）
    const timeoutId = setTimeout(scrollToBottom, 150);

    return () => clearTimeout(timeoutId);
  }, [messages, isThinking, scrollToBottom]); // messagesまたは思考中状態が変更されたときにスクロール

  // エージェント切り替え時のVRM連動（改良版・無限ループ修正）
  useEffect(() => {
    if (currentAgent && isClientMounted) {
      console.log(`🔄 エージェント切り替え: ${currentAgent.name} (${currentAgent.id})`);

      // setCurrentAgentを削除（無限ループの原因）
      // setCurrentAgent(currentAgentId);

      // CharacterContextのsetCurrentAgentを呼び出し（名前競合を避けるためCharacterContext版を明示的に使用）
      setCurrentAgent(currentAgentId); // この行はCharacterContext由来
      loadAgentSettings(currentAgentId);

      // VRM設定をログ出力（デバッグ用強化）
      const currentVRM = getCurrentAgentVRM();
      console.log(`📋 ${currentAgent.id}のVRM状況:`, {
        hasVRM: !!currentVRM,
        modelName: currentVRM?.name,
        modelType: currentVRM?.type,
        arrayBufferSize: currentVRM?.arrayBuffer?.byteLength,
        hasArrayBuffer: !!currentVRM?.arrayBuffer,
        hasUrl: !!currentVRM?.url,
        isLoaded: currentVRM?.isLoaded
      });

      // CharacterContextの状態も確認
      console.log(`📋 CharacterContext状態:`, {
        currentAgentId: characterState.currentAgentId,
        agentVRMSettings: characterState.agentVRMSettings[currentAgent.id],
        allAgentIds: Object.keys(characterState.agentVRMSettings)
      });
    }
  }, [currentAgentIndex, currentAgent.id, isClientMounted]);

  // エージェント切り替え時のシステムプロンプト確認（デバッグ用）
  useEffect(() => {
    if (currentAgent && isClientMounted) {
      const currentPrompt = systemPrompts[currentAgent.id];
      console.log(`📝 エージェント「${currentAgent.name}」のシステムプロンプト状態:`, {
        agentId: currentAgent.id,
        hasPrompt: !!currentPrompt,
        promptLength: currentPrompt?.length || 0,
        promptPreview: currentPrompt ? currentPrompt.substring(0, 200) + '...' : '未設定',
        allPromptsCount: Object.keys(systemPrompts).length
      });
    }
  }, [currentAgent.id, systemPrompts, isClientMounted]);

  // 音声入力統合
  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    isSupported
  } = useContinuousVoiceInput({
    onResult: (text) => {
      setMessage(text);
    }
  });

  // 時刻更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString('ja-JP', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // セッション保存・復元
  useEffect(() => {
    const savedSessions = localStorage.getItem('is-system-sessions');
    if (savedSessions) {
      try {
        const parsed = JSON.parse(savedSessions);
        setSessionStorage(parsed);

        if (parsed[currentAgent.id]) {
          setMessages(parsed[currentAgent.id]);
        }
      } catch (error) {
        console.error('セッション復元エラー:', error);
      }
    }

    // VRM設定の復元
    const savedVrmSettings = localStorage.getItem('is-system-vrm-settings');
    if (savedVrmSettings) {
      try {
        setVrmSettings(JSON.parse(savedVrmSettings));
      } catch (error) {
        console.error('VRM設定復元エラー:', error);
      }
    }

    // 音声自動再生設定の復元
    const savedAutoSpeech = localStorage.getItem('is-system-auto-speech');
    if (savedAutoSpeech) {
      try {
        setAutoSpeechEnabled(JSON.parse(savedAutoSpeech));
      } catch (error) {
        console.error('音声自動再生設定復元エラー:', error);
      }
    }

    // システムプロンプト設定の復元
    const savedSystemPrompts = localStorage.getItem('is-system-prompts');
    if (savedSystemPrompts) {
      try {
        setSystemPrompts(JSON.parse(savedSystemPrompts));
      } catch (error) {
        console.error('システムプロンプト復元エラー:', error);
      }
    } else {
      // デフォルトのシステムプロンプトを設定
      const defaultPrompts: {[key: string]: string} = {};
      agents.forEach(agent => {
        defaultPrompts[agent.id] = `あなたは${agent.name}（${agent.role}）です。

## 基本設定
- 役割: ${agent?.title || '未設定'}
- 性格: ${agent?.persona?.core || '未設定'}
- 専門分野: ${agent?.persona?.expertise?.join(', ') || '未設定'}

## コミュニケーションスタイル
${agent?.persona?.communicationStyle || '未設定'}

## 性格特性
${agent?.persona?.personality?.map(trait => `- ${trait}`).join('\n') || '- 未設定'}

## 感情表現範囲
${agent?.persona?.emotionalRange?.map(emotion => `- ${emotion}`).join('\n') || '- 未設定'}

## 指示
- 常にあなたの役割と専門性を活かして回答してください
- ${agent.name}らしい口調と視点で対話してください
- 必要に応じて専門知識を活用してください`;
      });
      setSystemPrompts(defaultPrompts);
      localStorage.setItem('is-system-prompts', JSON.stringify(defaultPrompts));
    }
  }, []);

  // メッセージ変更時の自動保存
  const saveToStorage = useCallback((agentId: string, newMessages: any[]) => {
    setSessionStorage(prev => {
      const updatedStorage = {
        ...prev,
        [agentId]: newMessages
      };
      localStorage.setItem('is-system-sessions', JSON.stringify(updatedStorage));
      return updatedStorage;
    });
  }, []);

  // エージェント切り替え時のメッセージ更新（無限ループ修正）
  useEffect(() => {
    if (sessionStorage[currentAgent.id] && sessionStorage[currentAgent.id].length > 0) {
      setMessages(sessionStorage[currentAgent.id]);
    } else {
      const welcomeMessage = {
        id: `welcome-${currentAgent.id}-${Date.now()}`,
        text: `私は${currentAgent.name}（${currentAgent.role}）です。${currentAgent.description}`,
        sender: 'agent' as const,
        timestamp: new Date(),
        agentId: currentAgent.id
      };
      const newMessages = [welcomeMessage];
      setMessages(newMessages);
      // saveToStorageを削除（無限ループの原因）
      // saveToStorage(currentAgent.id, newMessages);
    }
  }, [currentAgent.id]);

  const sendMessage = async () => {
    if (!message.trim()) return;

    // メッセージを即座にクリア（送信処理の最初に実行）
    const messageToSend = message.trim();
    setMessage('');

    const userMessage = {
      id: Date.now(),
      text: messageToSend,
      sender: 'user' as const,
      timestamp: new Date(),
      agentId: currentAgent.id
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    updateAgentMessage(currentAgentId, messageToSend);
    setActive(true);
    setAgentEmotion(currentAgentId, 'thinking');

    // メッセージ送信後に即座にスクロール
    setTimeout(scrollToBottom, 50);
    playAgentAnimation(currentAgentId, 'think');

    // 思考中ステータス表示
    setIsThinking(true);
    setThinkingMessage(`${currentAgent.name}が思考中...`);

    try {
      // カスタムシステムプロンプトがあるかチェック
      const customPrompt = systemPrompts[currentAgent.id];

      // デバッグ用ログ（詳細化）
      console.log(`🤖 ${currentAgent.name}へのメッセージ送信:`, {
        message: messageToSend,
        agentId: currentAgent.id,
        agentName: currentAgent.name,
        agentRole: currentAgent.role,
        hasCustomPrompt: !!customPrompt,
        customPromptLength: customPrompt?.length || 0,
        customPromptPreview: customPrompt ? customPrompt.substring(0, 100) + '...' : 'なし',
        allSystemPrompts: Object.keys(systemPrompts),
        currentAgentPromptExists: currentAgent.id in systemPrompts
      });

      // キャラクター同期コールバック関数
      const handleCharacterUpdate = (agentId: string, data: { emotion: string, message: string, isActive: boolean }) => {
        console.log(`🎭 キャラクター同期: ${agentId}`, data);
        
        // CharacterContext経由でVRM状態を更新
        setAgentEmotion(agentId, data.emotion as any);
        updateAgentMessage(agentId, data.message);
        
        // ローカル状態も更新
        setCurrentEmotion(data.emotion as any);
        setIsThinking(data.isActive);
        setThinkingMessage(data.isActive ? data.message : '');
        
        // VRM表示状態を更新
        if (data.isActive) {
          setVrmSettings(prev => ({
            ...prev,
            [agentId]: {
              lipSync: 85,
              expression: 90
            }
          }));
        } else {
          setVrmSettings(prev => ({
            ...prev,
            [agentId]: {
              lipSync: 20,
              expression: 50
            }
          }));
        }
      };

      // システムプロンプトが設定されているかの最終確認
      if (!customPrompt && currentAgent.id !== 'mother-cto') {
        console.warn(`⚠️ ${currentAgent.name}にシステムプロンプトが設定されていません`);
      }

      console.log(`🚀 chatWithMother呼び出し:`, {
        agentId: currentAgent.id,
        hasCustomPrompt: !!customPrompt,
        customPromptLength: customPrompt?.length || 0,
        selectedModel: currentAgent?.selectedModel || displayAgent.llmModel
      });

      const response = await chatWithMother(messageToSend, {
        messages: updatedMessages.map(m => ({
          role: m.sender === 'user' ? 'user' : 'assistant',
          content: m.text
        })),
        agentId: currentAgent.id,
        customSystemPrompt: customPrompt,
        selectedModel: currentAgent?.selectedModel || displayAgent.llmModel,
        activeProjects: 3,
        activeAgents: 15,
        systemStatus: '正常',
        isTerminal: false  // チャットビューからの呼び出し
      }, handleCharacterUpdate);

      setAgentEmotion(currentAgentId, 'speaking');
      playAgentAnimation(currentAgentId, 'speak');
      
      const agentResponse = {
        id: Date.now() + 1,
        text: response,
        sender: 'agent' as const,
        timestamp: new Date(),
        agentId: currentAgent.id
      };
      
      const finalMessages = [...updatedMessages, agentResponse];
      setMessages(finalMessages);
      saveToStorage(currentAgent.id, finalMessages);
      
      setAgentEmotion(currentAgentId, 'neutral');
      playAgentAnimation(currentAgentId, 'idle');

      // 音声自動再生が有効な場合のみ自動で音声再生
      if (autoSpeechEnabled) {
        setTimeout(() => {
          speakText(response, currentAgent.id);
        }, 500);
      }

      // 思考中ステータス解除
      setIsThinking(false);
      setThinkingMessage('');
    } catch (error) {
      console.error('Chat error:', error);
      setAgentEmotion(currentAgentId, 'neutral');

      // エラー時も思考中ステータス解除
      setIsThinking(false);
      setThinkingMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const switchAgent = (direction: 'prev' | 'next') => {
    // 現在のセッションをZustandに保存
    if (currentAgent?.id) {
      addToAgentHistory(currentAgent.id, messages);
    }
    
    // Zustand管理のエージェント切り替え
    const currentIndex = agents.findIndex(agent => agent.id === currentAgentId);
    let newIndex: number;
    
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : agents.length - 1;
    } else {
      newIndex = currentIndex < agents.length - 1 ? currentIndex + 1 : 0;
    }
    
    const newAgentId = agents[newIndex]?.id;
    if (newAgentId) {
      setZustandCurrentAgent(newAgentId); // Zustand版のsetCurrentAgentを使用
      console.log(`🔄 エージェント切り替え: ${agents[newIndex].name} (Zustand管理)`);
    }
  };

  // 入力欄リサイズハンドラー
  const handleInputMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    
    const startY = e.clientY;
    const startHeight = inputHeight;
    
    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = startY - e.clientY;
      const newHeight = Math.max(40, Math.min(200, startHeight + deltaY));
      setInputHeight(newHeight);
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // VRMビューワーリサイズハンドラー
  const handleVrmMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsVrmResizing(true);

    const startY = e.clientY;
    const startHeight = vrmViewerHeight;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = e.clientY - startY;
      const newHeight = Math.max(200, Math.min(800, startHeight + deltaY));
      
      // 比率ベースでの管理に変更
      if (parentContainerHeight > 0) {
        const availableHeight = parentContainerHeight - 200;
        const newRatio = Math.max(0.25, Math.min(0.8, newHeight / availableHeight));
        setVrmHeightRatio(newRatio);
        console.log(`🖱️ 手動リサイズ: 高さ${newHeight}px → 比率${newRatio.toFixed(2)}`);
      } else {
        // フォールバック（従来の方法）
        setVrmViewerHeight(newHeight);
      }

      // VRMビューワーに強制レンダリングを要求
      setVrmNeedsRender(prev => !prev);
    };

    const handleMouseUp = () => {
      setIsVrmResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // リサイズ完了時にも強制レンダリング
      setTimeout(() => {
        setVrmNeedsRender(prev => !prev);
      }, 100);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <div ref={containerRef} className="h-full flex flex-col bg-base-100">
      {/* エージェント切り替えヘッダー（エージェント別カラー対応） */}
      <div className={`p-4 border-b border-base-content/10 ${getAgentHeaderStyle(displayAgent.color)}`}>
        <div className="flex items-center justify-between">
          <button 
            className="btn btn-ghost btn-circle btn-sm"
            onClick={() => switchAgent('prev')}
          >
            <ChevronLeft size={16} />
          </button>
          
          <div className="text-center flex-1">
            <div className="flex items-center justify-center gap-3 mb-2">
              <div className={`text-3xl animate-pulse`}>
                {displayAgent.emoji}
              </div>
              <div>
                <h2 className="text-xl font-bold">{displayAgent.name}</h2>
                <p className="text-sm text-base-content/70">{displayAgent.title}</p>
              </div>
            </div>
            
            {/* エージェントステータス表示 */}
            <div className="flex justify-center gap-2 mt-2">
              {Object.entries(displayAgent.stats).map(([key, value]) => (
                <div key={key} className="text-xs">
                  <div className="w-12 h-1 bg-base-300 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-primary transition-all duration-300"
                      style={{ width: `${value}%` }}
                    />
                  </div>
                  <span className="text-[10px] opacity-70 capitalize">{key}</span>
                </div>
              ))}
            </div>
            
            <div className="flex justify-center gap-1 mt-2">
              {agents.map((_, index) => (
                <div 
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all ${
                    getAgentIndicatorStyle(displayAgent.color, index === currentAgentIndex)
                  }`}
                />
              ))}
            </div>
          </div>
          
          <button 
            className="btn btn-ghost btn-circle btn-sm"
            onClick={() => switchAgent('next')}
          >
            <ChevronRight size={16} />
          </button>
        </div>
        
        {/* エージェント情報 */}
        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="text-xs text-base-content/60 bg-base-200 px-2 py-1 rounded-full">
              {displayAgent.llmModel || currentAgent?.selectedModel || 'モデル未設定'}
            </div>
            <div className={`text-xs px-2 py-1 rounded-full ${getAgentBadgeStyle(displayAgent.color)}`}>
              {displayAgent.role}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* VRM状態表示とビューモード切り替え */}
            {getCurrentAgentVRM() ? (
              <div className="flex items-center gap-1">
                <div className="bg-success/20 text-success text-xs px-2 py-1 rounded-full border border-success/30 flex items-center gap-1">
                  <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                  VRM
                </div>
                {/* ビューモードトグル */}
                <div className="btn-group">
                  <button
                    className={`btn btn-xs ${vrmViewMode === 'bust' ? 'btn-primary' : 'btn-outline'}`}
                    onClick={() => setVrmViewMode('bust')}
                    title="バストアップ表示"
                  >
                    👤
                  </button>
                  <button
                    className={`btn btn-xs ${vrmViewMode === 'full' ? 'btn-primary' : 'btn-outline'}`}
                    onClick={() => setVrmViewMode('full')}
                    title="全身表示"
                  >
                    🔍
                  </button>
                </div>
                {/* 更新ボタン */}
                <button
                  className="btn btn-xs btn-ghost"
                  onClick={() => {
                    console.log('🔄 VRM手動更新実行');
                    setVrmNeedsRender(true);
                    // 強制的にVRMViewerを再レンダリング
                    setTimeout(() => setVrmNeedsRender(false), 100);
                  }}
                  title="VRM画面を更新"
                >
                  🔄
                </button>
              </div>
            ) : (
              <div className="bg-warning/20 text-warning text-xs px-2 py-1 rounded-full border border-warning/30">
                VRM未設定
              </div>
            )}
            <button
              className="btn btn-ghost btn-circle btn-sm"
              onClick={() => setShowSettings(!showSettings)}
              title="エージェント設定"
            >
              <Settings size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* 設定パネル（過去実装完全復元） */}
      {showSettings && (
        <div className="border-b border-base-content/10 bg-base-200/50">
          {/* 設定タブ */}
          <div className="flex border-b border-base-content/10">
            <button 
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'avatar' ? 'bg-primary/20 text-primary' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'avatar' ? null : 'avatar')}
            >
              <Eye size={14} className="inline mr-2" />
              外見
            </button>
            <button 
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'soul' ? 'bg-secondary/20 text-secondary' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'soul' ? null : 'soul')}
            >
              <Heart size={14} className="inline mr-2" />
              内面
            </button>
            <button
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'capability' ? 'bg-accent/20 text-accent' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'capability' ? null : 'capability')}
            >
              <Cpu size={14} className="inline mr-2" />
              機能
            </button>
            <button
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'voice' ? 'bg-info/20 text-info' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'voice' ? null : 'voice')}
            >
              <Mic size={14} className="inline mr-2" />
              音声
            </button>
            <button
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'plugins' ? 'bg-success/20 text-success' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'plugins' ? null : 'plugins')}
            >
              <Puzzle size={14} className="inline mr-2" />
              プラグイン
            </button>
          </div>

          {/* 設定コンテンツ（過去実装準拠） */}
          {activeSettingsTab && (
            <div className="p-4 space-y-3">
              {activeSettingsTab === 'avatar' && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold">外見設定</h3>
                    <button 
                      className="btn btn-xs btn-ghost"
                      onClick={() => setExpandedAvatarSettings(!expandedAvatarSettings)}
                      title="詳細設定を展開"
                    >
                      🔧 {expandedAvatarSettings ? '隠す' : '詳細'}
                    </button>
                  </div>
                  
                  {/* VRM管理（過去実装準拠） */}
                  <div className="space-y-2 text-sm">
                    <div>
                      <label className="label-text text-xs">VRMモデル</label>
                      <select 
                        className="select select-bordered select-xs w-full"
                        value={getCurrentAgentVRM() ? 'uploaded' : 'default'}
                        onChange={(e) => {
                          console.log('モデル変更:', e.target.value);
                          if (e.target.value !== 'uploaded') {
                            deleteAgentVRMModel(currentAgentId);
                          }
                        }}
                      >
                        <option value="default">デフォルトモデル</option>
                        <option value="business-woman">Alicia (ビジネスウーマン)</option>
                        <option value="engineer-girl">Mai (エンジニア女子)</option>
                        <option value="manager-man">Takeshi (マネージャー)</option>
                        <option value="custom1">カスタム1 (.vrmファイル)</option>
                        <option value="custom2">カスタム2 (.vrmファイル)</option>
                        {getCurrentAgentVRM() && (
                          <option value="uploaded">アップロード済み (.vrm)</option>
                        )}
                      </select>
                    </div>
                    
                    <div>
                      <label className="label-text text-xs">リップシンク強度</label>
                      <input 
                        type="range" 
                        className="range range-xs" 
                        min="0" 
                        max="100" 
                        value={vrmSettings[currentAgent.id]?.lipSync || 80}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          setVrmSettings(prev => {
                            const newSettings = {
                              ...prev,
                              [currentAgent.id]: {
                                ...prev[currentAgent.id],
                                lipSync: value
                              }
                            };
                            localStorage.setItem('is-system-vrm-settings', JSON.stringify(newSettings));
                            return newSettings;
                          });
                        }}
                      />
                      <div className="text-xs text-base-content/50 mt-1">{vrmSettings[currentAgent.id]?.lipSync || 80}%</div>
                    </div>
                    
                    <div>
                      <label className="label-text text-xs">表情の豊かさ</label>
                      <input 
                        type="range" 
                        className="range range-xs" 
                        min="0" 
                        max="100" 
                        value={vrmSettings[currentAgent.id]?.expression || 90}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          setVrmSettings(prev => {
                            const newSettings = {
                              ...prev,
                              [currentAgent.id]: {
                                ...prev[currentAgent.id],
                                expression: value
                              }
                            };
                            localStorage.setItem('is-system-vrm-settings', JSON.stringify(newSettings));
                            return newSettings;
                          });
                        }}
                      />
                      <div className="text-xs text-base-content/50 mt-1">{vrmSettings[currentAgent.id]?.expression || 90}%</div>
                    </div>
                    
                    <div>
                      <label className="label-text text-xs">VRMファイルアップロード</label>
                      <input 
                        type="file" 
                        accept=".vrm"
                        className="file-input file-input-bordered file-input-xs w-full"
                        onChange={async (e) => {
                          if (e.target.files?.[0]) {
                            const file = e.target.files[0];
                            console.log('🎨 VRMアップロード:', file.name, '→', currentAgent.name);
                            
                            try {
                              await uploadVRMModelForAgent(currentAgentId, file);
                              saveAgentSettings(currentAgentId);
                              
                              // 通知なし
                            } catch (error) {
                              console.error('VRMアップロードエラー:', error);
                            }
                          }
                        }}
                      />
                      {getCurrentAgentVRM() && (
                        <div className="flex items-center justify-between text-xs text-success mt-1">
                          <span>✓ {currentAgent.name}用VRMモデル設定済み</span>
                          <button 
                            className="btn btn-xs btn-error btn-outline"
                            onClick={() => {
                              if (confirm(`${currentAgent.name}のVRMモデルを削除しますか？`)) {
                                deleteAgentVRMModel(currentAgentId);
                                saveAgentSettings(currentAgentId);
                                console.log(`🗑️ ${currentAgent.name}のVRMモデルを削除`);
                              }
                            }}
                          >
                            🗑️ 削除
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* 詳細設定（展開時のみ表示） */}
                  {expandedAvatarSettings && (
                    <div className="mt-4 p-3 bg-base-200/50 rounded-lg border border-base-content/10 space-y-3">
                      <h4 className="text-xs font-semibold text-base-content/80">詳細設定</h4>
                      
                      {/* ポーズ・アニメーション制御 */}
                      <div>
                        <label className="label-text text-xs">ポーズ・アニメーション</label>
                        <div className="flex gap-2 mt-1">
                          <button 
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              console.log('ポーズリセット実行');
                              playAgentAnimation(currentAgentId, 'reset');
                            }}
                          >
                            🔄 リセット
                          </button>
                          <button 
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              playAgentAnimation(currentAgentId, 'idle');
                            }}
                          >
                            😐 待機
                          </button>
                          <button 
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              playAgentAnimation(currentAgentId, 'speak');
                            }}
                          >
                            👋 挨拶
                          </button>
                        </div>
                      </div>
                      
                      {/* VRM情報表示 */}
                      {getCurrentAgentVRM() && (
                        <div className="border-t border-base-content/10 pt-2">
                          <label className="label-text text-xs">VRM情報</label>
                          <div className="text-xs text-base-content/60 space-y-1">
                            <div>✓ モデル読み込み済み</div>
                            <div>📁 モデル名: {getCurrentAgentVRM()?.name}</div>
                            <div>🎛️ リップシンク: {vrmSettings[currentAgent.id]?.lipSync || 80}%</div>
                            <div>😊 表情強度: {vrmSettings[currentAgent.id]?.expression || 90}%</div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeSettingsTab === 'soul' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">内面設定</h3>

                  {/* ペルソナ表示 */}
                  <div className="bg-base-100/50 rounded-lg p-3 space-y-2">
                    <div className="text-xs font-medium">現在のペルソナ</div>
                    <p className="text-xs text-base-content/80">{currentAgent?.persona?.core || 'ペルソナ設定中...'}</p>
                    <div className="flex flex-wrap gap-1">
                      {currentAgent?.persona?.personality?.map((trait, index) => (
                        <span key={index} className="badge badge-outline badge-xs">{trait}</span>
                      )) || null}
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div>
                      <label className="label-text text-xs">LLMモデル</label>
                      <select 
                        className="select select-bordered select-xs w-full" 
                        value={currentAgent?.selectedModel || displayAgent.llmModel}
                        onChange={(e) => {
                          const newModel = e.target.value;
                          setAgentModel(currentAgent?.id || 'mother-cto', newModel);
                          console.log(`🤖 ${currentAgent?.name}のモデルを${newModel}に変更（Zustand管理）`);
                        }}
                      >
                        <option value="claude-3-haiku">Claude 3 Haiku (高速)</option>
                        <option value="claude-3-sonnet">Claude 3 Sonnet (バランス)</option>
                        <option value="claude-3-opus">Claude 3 Opus (高品質)</option>
                        <option value="gpt-4">GPT-4</option>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                      </select>
                    </div>

                    {/* システムプロンプト設定 */}
                    <div className="mt-4">
                      <label className="label-text text-xs font-medium">システムプロンプト</label>
                      <div className="text-xs text-base-content/60 mb-2">
                        {currentAgent.name}の行動指針と性格を定義するプロンプトです
                      </div>
                      <textarea
                        className="textarea textarea-bordered w-full text-xs"
                        rows={8}
                        placeholder={`${currentAgent.name}のシステムプロンプトを入力...`}
                        value={systemPrompts[currentAgent.id] || ''}
                        onChange={(e) => {
                          const newPrompts = {
                            ...systemPrompts,
                            [currentAgent.id]: e.target.value
                          };
                          setSystemPrompts(newPrompts);
                          localStorage.setItem('is-system-prompts', JSON.stringify(newPrompts));
                        }}
                      />
                      <div className="flex justify-between items-center mt-2">
                        <div className="text-xs text-base-content/50">
                          文字数: {(systemPrompts[currentAgent.id] || '').length}
                        </div>
                        <div className="flex gap-2">
                          <button
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              const defaultPrompt = `あなたは${currentAgent.name}（${currentAgent.role}）です。

## 基本設定
- 役割: ${currentAgent?.title || '未設定'}
- 性格: ${currentAgent?.persona?.core || '未設定'}
- 専門分野: ${currentAgent?.persona?.expertise?.join(', ') || '未設定'}

## コミュニケーションスタイル
${currentAgent?.persona?.communicationStyle || '未設定'}

## 性格特性
${currentAgent?.persona?.personality?.map(trait => `- ${trait}`).join('\n') || '- 未設定'}

## 感情表現範囲
${currentAgent?.persona?.emotionalRange?.map(emotion => `- ${emotion}`).join('\n') || '- 未設定'}

## 指示
- 常にあなたの役割と専門性を活かして回答してください
- ${currentAgent.name}らしい口調と視点で対話してください
- 必要に応じて専門知識を活用してください`;

                              const newPrompts = {
                                ...systemPrompts,
                                [currentAgent.id]: defaultPrompt
                              };
                              setSystemPrompts(newPrompts);
                              localStorage.setItem('is-system-prompts', JSON.stringify(newPrompts));
                            }}
                          >
                            🔄 デフォルトに戻す
                          </button>
                          <button
                            className="btn btn-xs btn-success"
                            onClick={() => {
                              // システムプロンプトの保存確認
                              console.log(`💾 ${currentAgent.name}のシステムプロンプトを保存:`, systemPrompts[currentAgent.id]);
                              alert(`${currentAgent.name}のシステムプロンプトを保存しました`);
                            }}
                          >
                            💾 保存
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSettingsTab === 'capability' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">機能設定</h3>
                  <div className="space-y-2">
                    <div className="text-xs font-medium">専門能力</div>
                    <div className="flex flex-wrap gap-1">
                      {currentAgent.capabilities.map((cap, index) => (
                        <span key={index} className="badge badge-secondary badge-xs">
                          {cap}
                        </span>
                      ))}
                    </div>
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      <button className="btn btn-xs btn-outline">
                        <BarChart3 size={10} />
                        学習統計
                      </button>
                      <button className="btn btn-xs btn-outline">
                        <Brain size={10} />
                        記憶管理
                      </button>
                    </div>

                    <div className="border-t border-base-content/10 pt-3 mt-3">
                      <div className="text-xs font-medium mb-2">セッション管理</div>
                      <div className="grid grid-cols-1 gap-2">
                        <button
                          className="btn btn-xs btn-warning"
                          onClick={() => {
                            const updatedMessages = [];
                            setMessages(updatedMessages);
                            saveToStorage(currentAgent.id, updatedMessages);
                          }}
                        >
                          現在のエージェントをリセット
                        </button>
                        <button
                          className="btn btn-xs btn-error"
                          onClick={() => {
                            if (confirm('全てのエージェントの会話履歴を削除しますか？')) {
                              localStorage.removeItem('is-system-sessions');
                              setSessionStorage({});
                              setMessages([]);
                            }
                          }}
                        >
                          全セッションを削除
                        </button>
                        <div className="text-xs text-base-content/50 mt-1">
                          保存されたエージェント数: {Object.keys(sessionStorage).length}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSettingsTab === 'voice' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">音声処理設定</h3>

                  {/* 音声自動再生設定 */}
                  <div className="bg-base-100/50 rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium">音声自動再生</div>
                        <div className="text-xs text-base-content/60">
                          エージェントの回答を自動で音声読み上げ
                        </div>
                      </div>
                      <label className="cursor-pointer">
                        <input
                          type="checkbox"
                          className="toggle toggle-primary"
                          checked={autoSpeechEnabled}
                          onChange={(e) => {
                            const enabled = e.target.checked;
                            setAutoSpeechEnabled(enabled);
                            localStorage.setItem('is-system-auto-speech', JSON.stringify(enabled));
                            console.log(`🔊 音声自動再生: ${enabled ? 'ON' : 'OFF'}`);
                          }}
                        />
                      </label>
                    </div>
                    {autoSpeechEnabled && (
                      <div className="text-xs text-success bg-success/10 rounded p-2">
                        ✅ エージェントの回答が自動で音声再生されます
                      </div>
                    )}
                  </div>

                  {/* 音声処理システム統合 */}
                  <VoiceProcessingSystem
                    onVolumeChange={(volume) => {
                      setVoiceVolume(volume);
                      // VRMのリップシンクに反映
                    }}
                    onTranscriptChange={(transcript) => {
                      setVoiceTranscript(transcript);
                      // 音声認識結果をチャットに自動入力
                      if (transcript.trim()) {
                        setMessage(transcript);
                      }
                    }}
                    isEnabled={true}
                    className="w-full"
                  />

                  {/* 音声認識結果表示 */}
                  {voiceTranscript && (
                    <div className="bg-base-100/50 rounded-lg p-3">
                      <div className="text-xs font-medium mb-2">最新の音声認識結果</div>
                      <p className="text-sm text-base-content/80">{voiceTranscript}</p>
                    </div>
                  )}

                  {/* 感情制御 */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium">感情表現制御</div>
                    <div className="grid grid-cols-3 gap-2">
                      {(['neutral', 'happy', 'sad', 'angry', 'surprised', 'thinking'] as const).map(emotion => (
                        <button
                          key={emotion}
                          className={`btn btn-xs ${
                            currentEmotion === emotion ? 'btn-primary' : 'btn-outline'
                          }`}
                          onClick={() => setCurrentEmotion(emotion)}
                        >
                          {emotion === 'neutral' ? '😐' :
                           emotion === 'happy' ? '😊' :
                           emotion === 'sad' ? '😢' :
                           emotion === 'angry' ? '😠' :
                           emotion === 'surprised' ? '😲' : '🤔'}
                          {emotion}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 音量レベル表示 */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium">リアルタイム音量</div>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 h-2 bg-base-300 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-primary transition-all duration-100"
                          style={{ width: `${voiceVolume * 100}%` }}
                        />
                      </div>
                      <span className="text-xs text-base-content/60 min-w-[3rem]">
                        {Math.round(voiceVolume * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {activeSettingsTab === 'plugins' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">プラグインシステム</h3>

                  {/* プラグインシステム統合 */}
                  <PluginSystem
                    onPluginChange={(type, plugin) => {
                      setActivePlugins(prev => ({
                        ...prev,
                        [type]: plugin
                      }));
                    }}
                    className="w-full h-96"
                  />

                  {/* アクティブプラグイン状態表示 */}
                  <div className="bg-base-100/50 rounded-lg p-3">
                    <div className="text-xs font-medium mb-2">アクティブプラグイン</div>
                    <div className="space-y-1">
                      {Object.entries(activePlugins).map(([type, plugin]) => (
                        <div key={type} className="flex justify-between items-center">
                          <span className="text-xs text-base-content/60">{type}:</span>
                          <span className="text-xs font-medium">
                            {plugin ? plugin.name : '未設定'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}


            </div>
          )}
        </div>
      )}

      {/* メッセージエリア（最適化レイアウト） */}
      <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
        
        {/* VRMビューワー（エージェント連動強化・リサイズ対応） */}
        <div 
          className="p-3 bg-base-200/20 border-b border-base-content/10 flex-shrink-0 relative"
          style={{ height: `${vrmViewerHeight}px` }}
        >
          <div className="h-full relative">
            {isClientMounted ? (
              <VRMViewer
                key={`vrm-${currentAgent.id}-${currentAgentIndex}`}
                vrmUrl={(() => {
                  const vrm = getCurrentAgentVRM();
                  console.log(`🔍 VRM URL取得: ${currentAgent.id}`, { hasVRM: !!vrm, url: vrm?.url });
                  return vrm?.url;
                })()}
                vrmArrayBuffer={(() => {
                  const vrm = getCurrentAgentVRM();
                  console.log(`🔍 VRM ArrayBuffer取得: ${currentAgent.id}`, { hasVRM: !!vrm, hasArrayBuffer: !!vrm?.arrayBuffer, size: vrm?.arrayBuffer?.byteLength });
                  return vrm?.arrayBuffer;
                })()}
                agentId={currentAgent.id}
                lipSyncValue={characterState.agentVRMSettings[currentAgentId]?.customSettings?.lipSyncEnabled ? (vrmSettings[currentAgent.id]?.lipSync || 80) : 0}
                expressionValue={vrmSettings[currentAgent.id]?.expression || 90}
                isActive={characterState.globalSettings.isActive}
                className="h-full w-full"
                showControls={false}
                viewMode={vrmViewMode}
                forceRender={vrmNeedsRender || isVrmResizing}
                onRenderComplete={() => setVrmNeedsRender(false)}
                // LocalAIVtuber & aituber-kit参考: 高度な制御
                emotion={currentEmotion}
                voiceVolume={voiceVolume}
                enableAutoBlinking={true}
                enableIdleAnimation={true}
                onExpressionChange={(expression, intensity) => {
                  console.log(`VRM表情変更: ${expression} (${intensity})`);
                }}
                onVRMUpload={async (file) => {
                  try {
                    console.log(`📋 VRMアップロード開始: ${currentAgent.name} - ${file.name} (${file.size} bytes)`);

                    await uploadVRMModelForAgent(currentAgent.id, file);
                    saveAgentSettings(currentAgent.id);

                    // アップロード後の状態確認
                    const uploadedVRM = getCurrentAgentVRM();
                    console.log(`✅ VRMアップロード完了: ${currentAgent.name}`, {
                      modelName: uploadedVRM?.name,
                      arrayBufferSize: uploadedVRM?.arrayBuffer?.byteLength,
                      hasArrayBuffer: !!uploadedVRM?.arrayBuffer
                    });

                    // 通知なし
                  } catch (error) {
                    console.error(`❌ VRMアップロードエラー (${currentAgent.name}):`, error);
                  }
                }}
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-base-300/20 rounded-lg">
                <div className="text-center">
                  <div className="loading loading-spinner loading-lg mb-2"></div>
                  <p className="text-sm text-base-content/60">初期化中...</p>
                </div>
              </div>
            )}
          </div>
          
          {/* VRMビューワー下部リサイズハンドラー */}
          <div
            className="absolute bottom-0 left-0 right-0 h-2 cursor-row-resize hover:bg-primary/30 transition-colors border-t border-base-content/10"
            onMouseDown={handleVrmMouseDown}
            title="VRMビューワーの高さを調整"
          >
            <div className="absolute left-1/2 top-1/2 h-0.5 w-8 -translate-x-1/2 -translate-y-1/2 bg-base-content/40 rounded-full opacity-60" />
          </div>
        </div>
        
        {/* チャットメッセージエリア */}
        <div ref={chatMessagesRef} className="flex-1 overflow-y-auto p-4 space-y-3 min-h-0 max-h-full">
          {messages.filter(msg => msg.agentId === currentAgent.id).map((msg) => (
            <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] p-3 rounded-2xl ${
                msg.sender === 'user'
                  ? 'bg-primary text-primary-content'
                  : 'bg-secondary/10 text-base-content border border-secondary/20'
              }`}>
                <div className="text-sm">{msg.text}</div>
                <div className="flex items-center justify-between mt-1">
                  <div className="text-xs opacity-70">
                    {msg.timestamp instanceof Date
                      ? msg.timestamp.toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
                      : new Date(msg.timestamp).toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
                    }
                  </div>
                  {msg.sender === 'agent' && (
                    <button
                      className="btn btn-xs btn-ghost btn-circle opacity-60 hover:opacity-100"
                      onClick={() => speakText(msg.text, msg.agentId)}
                      title="音声で読み上げ"
                    >
                      <Play size={12} />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}

          {/* 思考中ステータス表示 */}
          {isThinking && (
            <div className="flex justify-start">
              <div className="max-w-[80%] p-3 rounded-2xl bg-base-300/50 text-base-content border border-base-content/10">
                <div className="flex items-center gap-2">
                  <div className="loading loading-dots loading-sm"></div>
                  <p className="text-sm text-base-content/70">{thinkingMessage}</p>
                </div>
              </div>
            </div>
          )}

          {/* 初期メッセージ表示 */}
          {messages.filter(msg => msg.agentId === currentAgent.id).length === 0 && !isThinking && (
            <div className="text-center text-base-content/50 py-8">
              <p className="text-sm">{currentAgent.description}</p>
            </div>
          )}
        </div>
      </div>

      {/* 入力エリア（レスポンシブ対応・リサイズハンドラー付き） */}
      <div className="border-t border-base-content/10 bg-base-100 flex-shrink-0 relative">
        {/* 入力エリア上部リサイズハンドラー */}
        <div
          className="absolute top-0 left-0 right-0 h-2 cursor-row-resize hover:bg-primary/30 transition-colors"
          onMouseDown={handleInputMouseDown}
          title="入力エリアの高さを調整"
        >
          <div className="absolute left-1/2 top-1/2 h-0.5 w-8 -translate-x-1/2 -translate-y-1/2 bg-base-content/40 rounded-full opacity-60" />
        </div>
        <div className="p-4" style={{ height: `${Math.max(80, inputHeight + 32)}px` }}>
          <div className="flex gap-2 h-full">
            <div className="flex-1 min-w-0 flex flex-col">
              <textarea
                className="textarea textarea-bordered resize-none text-sm w-full"
                style={{ height: `${inputHeight}px` }}
                placeholder={`${currentAgent.name}に質問や指示を入力...`}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
              />
              {transcript && (
                <div className="text-xs text-base-content/50 mt-1 truncate">
                  音声認識: {transcript}
                </div>
              )}
            </div>
            
            <div className="flex flex-col gap-2 flex-shrink-0">
              <button
                className={`btn ${isListening ? 'btn-error' : 'btn-outline'}`}
                style={{ height: `${Math.max(32, inputHeight / 2)}px`, width: `48px` }}
                onClick={() => {
                  if (isListening) {
                    stopListening();
                  } else {
                    startListening();
                  }
                }}
                disabled={!isSupported}
                title={isListening ? '音声入力停止' : '音声入力開始'}
              >
                {isListening ? <MicOff size={20} /> : <Mic size={20} />}
              </button>
              
              <button
                className="btn btn-primary"
                style={{ height: `${Math.max(32, inputHeight / 2)}px`, width: `48px` }}
                onClick={sendMessage}
                disabled={!message.trim()}
                title="メッセージ送信"
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ISSystem;