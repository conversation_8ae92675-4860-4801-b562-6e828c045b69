'use client';

import { useState, useEffect } from 'react';
import { 
  ChevronRight, 
  ChevronDown, 
  Folder, 
  FolderOpen, 
  File, 
  FileText, 
  FileCode, 
  FileImage, 
  FileVideo,
  FileAudio,
  Archive,
  Settings,
  Search,
  MoreHorizontal,
  Plus,
  Edit3,
  Copy,
  Trash2,
  RefreshCw,
  FolderPlus,
  FileEdit
} from 'lucide-react';
import { useContextMenu, ContextMenuItem } from './ContextMenu';

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: FileNode[];
  isExpanded?: boolean;
  size?: number;
  lastModified?: Date;
  extension?: string;
}

interface FileExplorerProps {
  rootPath?: string;
  onFileSelect?: (file: FileNode) => void;
  onFileOpen?: (file: FileNode) => void;
  onFileCreate?: (parentPath: string, fileName: string, type: 'file' | 'folder') => void;
  onFileRename?: (file: FileNode, newName: string) => void;
  onFileDelete?: (file: FileNode) => void;
  className?: string;
}

export default function FileExplorer({ 
  rootPath = '/Users/<USER>/Dev/meta-studio', 
  onFileSelect, 
  onFileOpen,
  onFileCreate,
  onFileRename,
  onFileDelete,
  className = ""
}: FileExplorerProps) {
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set([rootPath]));
  const [editingNode, setEditingNode] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState<{ parentPath: string; type: 'file' | 'folder' } | null>(null);
  const [draggedNode, setDraggedNode] = useState<FileNode | null>(null);
  const [dragOverNode, setDragOverNode] = useState<string | null>(null);
  
  const { showContextMenu, ContextMenuComponent } = useContextMenu();

  // ファイル操作ハンドラー
  const handleNodeClick = (node: FileNode) => {
    setSelectedFile(node.id);
    onFileSelect?.(node);
    
    if (node.type === 'folder') {
      toggleFolder(node.id, node.path);
    } else {
      onFileOpen?.(node);
    }
  };

  const handleNodeDoubleClick = (node: FileNode) => {
    if (node.type === 'file') {
      onFileOpen?.(node);
    }
  };

  const handleNodeRename = (node: FileNode) => {
    setEditingNode(node.id);
    setEditingName(node.name);
  };

  const handleRenameSubmit = async (node: FileNode) => {
    if (editingName.trim() && editingName !== node.name) {
      try {
        const response = await fetch('/api/files', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            oldPath: node.path,
            newName: editingName.trim()
          })
        });

        if (response.ok) {
          // 成功時はファイルツリーを更新
          refreshFileTree();
          console.log(`✅ ファイル名変更成功: ${node.name} → ${editingName.trim()}`);
        } else {
          const error = await response.text();
          alert(`ファイル名変更に失敗しました: ${error}`);
        }
      } catch (error) {
        console.error('ファイル名変更エラー:', error);
        alert(`ファイル名変更エラー: ${error}`);
      }

      // コールバックも呼び出す（互換性のため）
      onFileRename?.(node, editingName.trim());
    }
    setEditingNode(null);
    setEditingName('');
  };

  const handleCreateFile = (parentPath: string, type: 'file' | 'folder') => {
    setShowCreateDialog({ parentPath, type });
  };

  const handleCreateSubmit = async (fileName: string) => {
    if (showCreateDialog && fileName.trim()) {
      try {
        const response = await fetch('/api/files', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: showCreateDialog.type === 'file' ? 'create_file' : 'create_folder',
            path: showCreateDialog.parentPath,
            name: fileName.trim(),
            content: showCreateDialog.type === 'file' ? '' : undefined
          })
        });

        if (response.ok) {
          // 成功時はファイルツリーを更新
          refreshFileTree();
          console.log(`✅ ${showCreateDialog.type === 'file' ? 'ファイル' : 'フォルダ'}作成成功: ${fileName.trim()}`);
        } else {
          const error = await response.text();
          alert(`${showCreateDialog.type === 'file' ? 'ファイル' : 'フォルダ'}作成に失敗しました: ${error}`);
        }
      } catch (error) {
        console.error('ファイル/フォルダ作成エラー:', error);
        alert(`作成エラー: ${error}`);
      }

      // コールバックも呼び出す（互換性のため）
      onFileCreate?.(showCreateDialog.parentPath, fileName.trim(), showCreateDialog.type);
    }
    setShowCreateDialog(null);
  };

  // ドラッグ&ドロップハンドラー
  const handleDragStart = (e: React.DragEvent, node: FileNode) => {
    setDraggedNode(node);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', node.path);
  };

  const handleDragOver = (e: React.DragEvent, node: FileNode) => {
    if (node.type === 'folder' && draggedNode && draggedNode.id !== node.id) {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
      setDragOverNode(node.id);
    }
  };

  const handleDragLeave = () => {
    setDragOverNode(null);
  };

  const handleDrop = (e: React.DragEvent, targetNode: FileNode) => {
    e.preventDefault();
    setDragOverNode(null);
    
    if (draggedNode && targetNode.type === 'folder' && draggedNode.id !== targetNode.id) {
      // ファイル移動の実行
      const sourcePath = draggedNode.path;
      const targetPath = `${targetNode.path}/${draggedNode.name}`;
      
      // 実際のファイル移動処理（APIを呼び出す）
      handleFileMove(draggedNode, targetNode);
    }
    
    setDraggedNode(null);
  };

  const handleFileMove = async (sourceNode: FileNode, targetNode: FileNode) => {
    try {
      const response = await fetch('/api/files', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'move',
          oldPath: sourceNode.path,
          newPath: `${targetNode.path}/${sourceNode.name}`
        })
      });

      if (response.ok) {
        // 成功時はファイルツリーを更新
        refreshFileTree();
        alert('ファイルを移動しました');
      } else {
        const error = await response.text();
        alert(`ファイル移動に失敗しました: ${error}`);
      }
    } catch (error) {
      alert(`ファイル移動エラー: ${error}`);
    }
  };

  const handleFileDeleteReal = async (node: FileNode) => {
    try {
      console.log('削除対象:', node.path);
      
      const response = await fetch(`/api/files?path=${encodeURIComponent(node.path)}`, {
        method: 'DELETE'
      });

      console.log('削除レスポンス:', response.status, response.statusText);

      if (response.ok) {
        const result = await response.json();
        console.log('削除成功:', result);
        // 成功時はファイルツリーを更新
        refreshFileTree();
        alert('ファイルを削除しました');
      } else {
        const errorText = await response.text();
        console.error('削除失敗:', errorText);
        alert(`削除に失敗しました: ${errorText}`);
      }
    } catch (error) {
      console.error('削除エラー:', error);
      alert(`削除エラー: ${error}`);
    }
  };

  const refreshFileTree = async () => {
    try {
      const response = await fetch(`/api/files?path=${encodeURIComponent(rootPath)}`);
      if (response.ok) {
        const data = await response.json();
        setFileTree(data.files || mockFileTree);
      }
    } catch (error) {
      console.warn('ファイルツリー更新に失敗、モックデータを使用:', error);
    }
  };

  const getFileContextMenu = (node: FileNode): ContextMenuItem[] => [
    {
      id: 'open',
      label: node.type === 'folder' ? 'フォルダを開く' : 'ファイルを開く',
      icon: '📂',
      action: () => handleNodeClick(node)
    },
    { id: 'separator1', label: '', separator: true, action: () => {} },
    {
      id: 'new-file',
      label: '新しいファイル',
      icon: '📄',
      action: () => handleCreateFile(node.type === 'folder' ? node.path : node.path.split('/').slice(0, -1).join('/'), 'file'),
      disabled: node.type === 'file'
    },
    {
      id: 'new-folder',
      label: '新しいフォルダ',
      icon: '📁',
      action: () => handleCreateFile(node.type === 'folder' ? node.path : node.path.split('/').slice(0, -1).join('/'), 'folder'),
      disabled: node.type === 'file'
    },
    { id: 'separator2', label: '', separator: true, action: () => {} },
    {
      id: 'rename',
      label: '名前を変更',
      icon: '✏️',
      action: () => handleNodeRename(node)
    },
    {
      id: 'copy',
      label: 'コピー',
      icon: '📋',
      action: () => navigator.clipboard.writeText(node.path)
    },
    {
      id: 'delete',
      label: '削除',
      icon: '🗑️',
      action: () => {
        if (confirm(`${node.name} を削除しますか？この操作は元に戻せません。`)) {
          handleFileDeleteReal(node);
        }
      }
    }
  ];

  // ファイルタイプ別アイコン取得
  // 統一ファイルカラーシステム（TabManagerと同じ）
  const getFileColorAndIcon = (fileName: string, isFolder: boolean = false) => {
    if (isFolder) {
      return { iconComponent: expandedFolders.has(fileName) ? <FolderOpen size={16} /> : <Folder size={16} />, colorClass: 'text-blue-400' };
    }

    const lowerName = fileName.toLowerCase();
    
    // TabManagerと同じカラープロファイル
    if (lowerName.endsWith('.md')) return { iconComponent: <FileText size={16} />, colorClass: 'text-info' };
    if (lowerName.endsWith('.yaml') || lowerName.endsWith('.yml')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-warning' };
    if (lowerName.endsWith('.json')) return { iconComponent: <FileText size={16} />, colorClass: 'text-success' };
    if (lowerName.endsWith('.ts') || lowerName.endsWith('.tsx')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-primary' };
    if (lowerName.endsWith('.js') || lowerName.endsWith('.jsx')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-accent' };
    if (lowerName.endsWith('.css')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-secondary' };
    if (lowerName.endsWith('.html')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-info' };
    if (lowerName.endsWith('.py')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-warning' };
    if (lowerName.endsWith('.toml')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-neutral' };
    if (lowerName.endsWith('.txt')) return { iconComponent: <FileText size={16} />, colorClass: 'text-base-content' };
    if (lowerName.endsWith('.env')) return { iconComponent: <Settings size={16} />, colorClass: 'text-error' };
    if (lowerName.endsWith('.gitignore') || lowerName.includes('git')) return { iconComponent: <FileCode size={16} />, colorClass: 'text-neutral' };
    if (lowerName.endsWith('.lock')) return { iconComponent: <Archive size={16} />, colorClass: 'text-error' };
    if (lowerName.includes('package') || lowerName.includes('config')) return { iconComponent: <Settings size={16} />, colorClass: 'text-primary' };
    if (lowerName.includes('readme')) return { iconComponent: <FileText size={16} />, colorClass: 'text-info' };
    if (lowerName.includes('license')) return { iconComponent: <FileText size={16} />, colorClass: 'text-neutral' };
    
    // メディアファイル
    if (lowerName.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return { iconComponent: <FileImage size={16} />, colorClass: 'text-success' };
    if (lowerName.match(/\.(mp4|avi|mov|webm|mkv)$/)) return { iconComponent: <FileVideo size={16} />, colorClass: 'text-secondary' };
    if (lowerName.match(/\.(mp3|wav|flac|aac|ogg)$/)) return { iconComponent: <FileAudio size={16} />, colorClass: 'text-warning' };
    if (lowerName.match(/\.(zip|tar|gz|rar|7z)$/)) return { iconComponent: <Archive size={16} />, colorClass: 'text-neutral' };
    
    return { iconComponent: <File size={16} />, colorClass: 'text-base-content' };
  };

  const getFileIcon = (node: FileNode) => {
    const { iconComponent, colorClass } = getFileColorAndIcon(node.name, node.type === 'folder');
    return <span className={colorClass}>{iconComponent}</span>;
  };

  // モックファイルツリーデータ
  const mockFileTree: FileNode[] = [
    {
      id: 'meta-studio',
      name: 'meta-studio',
      type: 'folder',
      path: '/Users/<USER>/Dev/meta-studio',
      isExpanded: true,
      children: [
        {
          id: 'apps',
          name: 'apps',
          type: 'folder',
          path: '/Users/<USER>/Dev/meta-studio/apps',
          children: [
            {
              id: 'web',
              name: 'web',
              type: 'folder',
              path: '/Users/<USER>/Dev/meta-studio/apps/web',
              children: [
                {
                  id: 'src',
                  name: 'src',
                  type: 'folder',
                  path: '/Users/<USER>/Dev/meta-studio/apps/web/src',
                  children: [
                    {
                      id: 'components',
                      name: 'components',
                      type: 'folder',
                      path: '/Users/<USER>/Dev/meta-studio/apps/web/src/components',
                      children: [
                        { id: 'TabManager.tsx', name: 'TabManager.tsx', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/src/components/TabManager.tsx', extension: '.tsx' },
                        { id: 'MetaStudioLayout.tsx', name: 'MetaStudioLayout.tsx', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/src/components/MetaStudioLayout.tsx', extension: '.tsx' },
                        { id: 'ISSystem.tsx', name: 'ISSystem.tsx', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/src/components/ISSystem.tsx', extension: '.tsx' },
                        { id: 'VRMViewer.tsx', name: 'VRMViewer.tsx', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/src/components/VRMViewer.tsx', extension: '.tsx' }
                      ]
                    },
                    {
                      id: 'lib',
                      name: 'lib',
                      type: 'folder',
                      path: '/Users/<USER>/Dev/meta-studio/apps/web/src/lib',
                      children: [
                        { id: 'utils.ts', name: 'utils.ts', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/src/lib/utils.ts', extension: '.ts' },
                        { id: 'ai-agents.ts', name: 'ai-agents.ts', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/src/lib/ai-agents.ts', extension: '.ts' }
                      ]
                    }
                  ]
                },
                { id: 'package.json', name: 'package.json', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/package.json', extension: '.json' },
                { id: 'next.config.js', name: 'next.config.js', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/next.config.js', extension: '.js' },
                { id: 'tailwind.config.js', name: 'tailwind.config.js', type: 'file', path: '/Users/<USER>/Dev/meta-studio/apps/web/tailwind.config.js', extension: '.js' }
              ]
            }
          ]
        },
        { id: 'README.md', name: 'README.md', type: 'file', path: '/Users/<USER>/Dev/meta-studio/README.md', extension: '.md' },
        { id: 'package.json-root', name: 'package.json', type: 'file', path: '/Users/<USER>/Dev/meta-studio/package.json', extension: '.json' },
        { id: 'task.md', name: 'task.md', type: 'file', path: '/Users/<USER>/Dev/meta-studio/task.md', extension: '.md' }
      ]
    }
  ];

  useEffect(() => {
    setFileTree(mockFileTree);
    // 初期展開状態を設定
    const initialExpanded = new Set([
      'meta-studio',
      '/Users/<USER>/Dev/meta-studio',
      'apps',
      '/Users/<USER>/Dev/meta-studio/apps'
    ]);
    setExpandedFolders(initialExpanded);
  }, []);

  const toggleFolder = (nodeId: string, nodePath?: string) => {
    const newExpanded = new Set(expandedFolders);
    
    // IDとPathの両方で管理
    const identifiers = [nodeId];
    if (nodePath) identifiers.push(nodePath);
    
    const isCurrentlyExpanded = identifiers.some(id => newExpanded.has(id));
    
    if (isCurrentlyExpanded) {
      // 展開中の場合は閉じる
      identifiers.forEach(id => newExpanded.delete(id));
    } else {
      // 閉じている場合は開く
      identifiers.forEach(id => newExpanded.add(id));
    }
    
    setExpandedFolders(newExpanded);
  };

  const toggleExpanded = (nodePath: string) => {
    toggleFolder(nodePath, nodePath);
  };

  const handleFileClick = (node: FileNode) => {
    setSelectedFile(node.id);
    onFileSelect?.(node);
  };

  const handleFileDoubleClick = (node: FileNode) => {
    if (node.type === 'file') {
      onFileOpen?.(node);
    }
  };

  const renderNode = (node: FileNode, depth: number = 0): React.ReactNode => {
    const isExpanded = expandedFolders.has(node.id) || expandedFolders.has(node.path) || node.isExpanded;
    const isSelected = selectedFile === node.id;
    const hasChildren = node.children && node.children.length > 0;
    const isEditing = editingNode === node.id;

    // 検索フィルター
    if (searchQuery && !node.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      // 子要素に一致するものがあるかチェック
      if (node.type === 'folder' && node.children) {
        const hasMatchingChild = node.children.some(child => 
          child.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
        if (!hasMatchingChild) return null;
      } else {
        return null;
      }
    }

    return (
      <div key={node.id}>
        <div
          className={`flex items-center gap-1 py-1 px-2 cursor-pointer hover:bg-base-200 transition-colors ${
            isSelected ? 'bg-primary/20 text-primary-content' : ''
          } ${dragOverNode === node.id ? 'bg-accent/30 border-l-4 border-accent' : ''} ${
            draggedNode?.id === node.id ? 'opacity-50' : ''
          }`}
          style={{ paddingLeft: `${depth * 12 + 8}px` }}
          draggable={true}
          onDragStart={(e) => handleDragStart(e, node)}
          onDragOver={(e) => handleDragOver(e, node)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, node)}
          onClick={() => handleNodeClick(node)}
          onDoubleClick={() => handleNodeDoubleClick(node)}
          onContextMenu={(e) => {
            e.preventDefault();
            showContextMenu(e, getFileContextMenu(node));
          }}
        >
          {/* 展開アイコン */}
          <div className="w-4 h-4 flex items-center justify-center">
            {node.type === 'folder' && hasChildren && (
              <button
                className="hover:bg-base-300 rounded"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFolder(node.id, node.path);
                }}
              >
                {isExpanded ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
              </button>
            )}
          </div>

          {/* ファイル/フォルダアイコン */}
          {getFileIcon(node)}

          {/* ファイル名（編集可能） */}
          {isEditing ? (
            <input
              type="text"
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
              onBlur={() => handleRenameSubmit(node)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleRenameSubmit(node);
                } else if (e.key === 'Escape') {
                  setEditingNode(null);
                  setEditingName('');
                }
              }}
              className="input input-xs flex-1 bg-base-100 border-primary"
              autoFocus
              onFocus={(e) => e.target.select()}
            />
          ) : (
            <span className={`text-sm font-medium truncate flex-1 ${getFileColorAndIcon(node.name, node.type === 'folder').colorClass}`}>
              {node.name}
            </span>
          )}

          {/* ファイルサイズ（ファイルのみ） */}
          {node.type === 'file' && node.size && (
            <span className="text-xs text-base-content/60">
              {(node.size / 1024).toFixed(1)}KB
            </span>
          )}
        </div>

        {/* 子要素の再帰表示 */}
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`h-full flex flex-col bg-base-100 ${className}`}>
      {/* ヘッダー */}
      <div className="flex items-center justify-between p-3 border-b border-base-content/10">
        <h3 className="text-sm font-semibold">エクスプローラー</h3>
        <div className="flex items-center gap-1">
          <button
            className="btn btn-xs btn-ghost btn-circle"
            title="新しいフォルダ"
            onClick={() => handleCreateFile(rootPath, 'folder')}
          >
            <Folder size={12} />
          </button>
          <button
            className="btn btn-xs btn-ghost btn-circle"
            title="新しいファイル"
            onClick={() => handleCreateFile(rootPath, 'file')}
          >
            <File size={12} />
          </button>
          <button
            className="btn btn-xs btn-ghost btn-circle"
            title="更新"
            onClick={refreshFileTree}
          >
            <Settings size={12} />
          </button>
          <button className="btn btn-xs btn-ghost btn-circle" title="その他">
            <MoreHorizontal size={12} />
          </button>
        </div>
      </div>

      {/* 検索バー */}
      <div className="p-2 border-b border-base-content/10">
        <div className="relative">
          <Search size={14} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-base-content/50" />
          <input
            type="text"
            placeholder="ファイル検索..."
            className="input input-xs w-full pl-8 bg-base-200/50 border-base-content/20"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* ファイルツリー */}
      <div className="flex-1 overflow-y-auto">
        {fileTree.map(node => renderNode(node))}
      </div>

      {/* ステータスバー */}
      <div className="p-2 border-t border-base-content/10 bg-base-200/30">
        <div className="flex items-center justify-between text-xs text-base-content/60">
          <span>
            {fileTree.length > 0 && `${fileTree[0].children?.length || 0} 項目`}
          </span>
          <span>{rootPath}</span>
        </div>
      </div>

      {/* ファイル作成ダイアログ */}
      {showCreateDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-base-100 p-4 rounded-lg shadow-xl min-w-[300px]">
            <h3 className="text-lg font-semibold mb-3">
              新しい{showCreateDialog.type === 'file' ? 'ファイル' : 'フォルダ'}を作成
            </h3>
            <input
              type="text"
              placeholder={`${showCreateDialog.type === 'file' ? 'ファイル' : 'フォルダ'}名を入力...`}
              className="input input-bordered w-full mb-3"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCreateSubmit(e.currentTarget.value);
                } else if (e.key === 'Escape') {
                  setShowCreateDialog(null);
                }
              }}
            />
            <div className="flex justify-end gap-2">
              <button
                className="btn btn-sm btn-outline"
                onClick={() => setShowCreateDialog(null)}
              >
                キャンセル
              </button>
              <button
                className="btn btn-sm btn-primary"
                onClick={(e) => {
                  const input = e.currentTarget.parentElement?.parentElement?.querySelector('input') as HTMLInputElement;
                  if (input) {
                    handleCreateSubmit(input.value);
                  }
                }}
              >
                作成
              </button>
            </div>
          </div>
        </div>
      )}

      {/* コンテキストメニュー */}
      <ContextMenuComponent />
    </div>
  );
}