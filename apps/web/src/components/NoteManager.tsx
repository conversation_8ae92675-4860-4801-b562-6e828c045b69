'use client';

import { useState, useEffect } from 'react';
import { Plus, Search, FileText, Edit3, Trash2, Tag, Calendar, BookOpen } from 'lucide-react';

interface Note {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  category: string;
}

export default function NoteManager() {
  const [notes, setNotes] = useState<Note[]>([]);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // デモ用のモックデータ
  useEffect(() => {
    const mockNotes: Note[] = [
      {
        id: '1',
        title: 'Meta Studio 要件定義',
        content: '# Meta Studio 要件定義\n\n## 概要\nAI駆動の統合開発環境...\n\n## 機能要件\n- VRMキャラクター統合\n- ターミナル機能\n- プロジェクト管理',
        tags: ['要件定義', 'プロジェクト', 'AI'],
        createdAt: new Date('2025-06-19'),
        updatedAt: new Date('2025-06-19'),
        category: 'プロジェクト'
      },
      {
        id: '2',
        title: 'VRMビューワー実装メモ',
        content: '# VRMビューワー実装\n\n## 技術スタック\n- Three.js\n- @pixiv/three-vrm\n\n## 実装課題\n- リサイズ対応\n- パフォーマンス最適化',
        tags: ['VRM', '技術', '実装'],
        createdAt: new Date('2025-06-18'),
        updatedAt: new Date('2025-06-19'),
        category: '技術'
      },
      {
        id: '3',
        title: 'タスク管理アイデア',
        content: '# タスク管理機能\n\n## アイデア\n- カンバン方式\n- 優先度設定\n- 進捗追跡\n\n## UI案\n- ドラッグ&ドロップ\n- 色分け表示',
        tags: ['UI', 'タスク管理', 'アイデア'],
        createdAt: new Date('2025-06-17'),
        updatedAt: new Date('2025-06-18'),
        category: 'アイデア'
      }
    ];
    setNotes(mockNotes);
  }, []);

  const categories = ['all', 'プロジェクト', '技術', 'アイデア', 'その他'];

  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || note.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const createNewNote = () => {
    const newNote: Note = {
      id: Date.now().toString(),
      title: '新しいノート',
      content: '# 新しいノート\n\nここに内容を記入...',
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      category: 'その他'
    };
    setNotes([newNote, ...notes]);
    setSelectedNote(newNote);
    setIsEditing(true);
    setShowCreateModal(false);
  };

  const deleteNote = (noteId: string) => {
    if (confirm('このノートを削除しますか？')) {
      setNotes(notes.filter(note => note.id !== noteId));
      if (selectedNote?.id === noteId) {
        setSelectedNote(null);
      }
    }
  };

  const saveNote = (updatedNote: Note) => {
    setNotes(notes.map(note => 
      note.id === updatedNote.id 
        ? { ...updatedNote, updatedAt: new Date() }
        : note
    ));
    setSelectedNote(updatedNote);
    setIsEditing(false);
  };

  return (
    <div className="h-full flex bg-base-100">
      {/* サイドバー - ノート一覧 */}
      <div className="w-1/3 border-r border-base-content/10 flex flex-col">
        {/* ヘッダー */}
        <div className="p-4 border-b border-base-content/10">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-bold flex items-center gap-2">
              <BookOpen size={20} />
              ノート管理
            </h2>
            <button
              onClick={createNewNote}
              className="btn btn-primary btn-sm"
            >
              <Plus size={16} />
              新規作成
            </button>
          </div>

          {/* 検索 */}
          <div className="relative mb-3">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/50" />
            <input
              type="text"
              placeholder="ノートを検索..."
              className="input input-sm w-full pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* カテゴリフィルター */}
          <select
            className="select select-sm w-full"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'すべて' : category}
              </option>
            ))}
          </select>
        </div>

        {/* ノート一覧 */}
        <div className="flex-1 overflow-y-auto">
          {filteredNotes.map(note => (
            <div
              key={note.id}
              className={`p-4 border-b border-base-content/5 cursor-pointer hover:bg-base-200/50 ${
                selectedNote?.id === note.id ? 'bg-primary/10 border-l-4 border-l-primary' : ''
              }`}
              onClick={() => setSelectedNote(note)}
            >
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-medium text-sm line-clamp-1">{note.title}</h3>
                <div className="flex gap-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedNote(note);
                      setIsEditing(true);
                    }}
                    className="btn btn-ghost btn-xs"
                  >
                    <Edit3 size={12} />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteNote(note.id);
                    }}
                    className="btn btn-ghost btn-xs text-error"
                  >
                    <Trash2 size={12} />
                  </button>
                </div>
              </div>
              
              <p className="text-xs text-base-content/60 line-clamp-2 mb-2">
                {note.content.replace(/^#\s+.*$/gm, '').substring(0, 100)}...
              </p>
              
              <div className="flex items-center justify-between text-xs text-base-content/50">
                <div className="flex items-center gap-1">
                  <Calendar size={10} />
                  {note.updatedAt.toLocaleDateString('ja-JP')}
                </div>
                <div className="flex gap-1">
                  {note.tags.slice(0, 2).map(tag => (
                    <span key={tag} className="badge badge-xs">{tag}</span>
                  ))}
                  {note.tags.length > 2 && <span className="text-xs">+{note.tags.length - 2}</span>}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* メインエリア - ノート表示/編集 */}
      <div className="flex-1 flex flex-col">
        {selectedNote ? (
          <>
            {/* ノートヘッダー */}
            <div className="p-4 border-b border-base-content/10">
              <div className="flex items-center justify-between mb-2">
                <input
                  type="text"
                  className={`text-xl font-bold bg-transparent border-none outline-none flex-1 ${
                    isEditing ? 'border-b border-base-content/20' : ''
                  }`}
                  value={selectedNote.title}
                  readOnly={!isEditing}
                  onChange={(e) => setSelectedNote({...selectedNote, title: e.target.value})}
                />
                <div className="flex gap-2">
                  {isEditing ? (
                    <>
                      <button
                        onClick={() => saveNote(selectedNote)}
                        className="btn btn-primary btn-sm"
                      >
                        保存
                      </button>
                      <button
                        onClick={() => setIsEditing(false)}
                        className="btn btn-outline btn-sm"
                      >
                        キャンセル
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="btn btn-outline btn-sm"
                    >
                      <Edit3 size={16} />
                      編集
                    </button>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-base-content/60">
                <span>作成: {selectedNote.createdAt.toLocaleDateString('ja-JP')}</span>
                <span>更新: {selectedNote.updatedAt.toLocaleDateString('ja-JP')}</span>
                <span>カテゴリ: {selectedNote.category}</span>
              </div>
            </div>

            {/* ノート内容 */}
            <div className="flex-1 p-4">
              {isEditing ? (
                <textarea
                  className="w-full h-full textarea textarea-bordered resize-none font-mono text-sm"
                  value={selectedNote.content}
                  onChange={(e) => setSelectedNote({...selectedNote, content: e.target.value})}
                  placeholder="ノートの内容を入力..."
                />
              ) : (
                <div className="h-full overflow-y-auto prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                    {selectedNote.content}
                  </pre>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-base-content/50">
            <div className="text-center">
              <FileText size={64} className="mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">ノートを選択してください</h3>
              <p className="text-sm">左のリストからノートを選択するか、新規作成してください</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}