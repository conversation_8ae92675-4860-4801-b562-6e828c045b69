import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { devtools } from 'zustand/middleware';

// AI Model Types
interface AIModelConfig {
  id: string;
  name: string;
  displayName: string;
  provider: 'anthropic' | 'openai' | 'local';
  tier: 'fast' | 'balanced' | 'premium';
  contextWindow: number;
  pricePerToken: {
    input: number;
    output: number;
  };
  capabilities: string[];
  maxResponseTime: number; // seconds
  availability: 'stable' | 'beta' | 'experimental';
}

// Agent State Types
interface AgentState {
  id: string;
  name: string;
  role: string;
  selectedModel: string;
  isActive: boolean;
  vrmSettings?: {
    modelPath?: string;
    emotion: 'neutral' | 'happy' | 'thinking' | 'speaking' | 'listening';
    visibility: boolean;
  };
  sessionHistory: any[];
  customPrompt?: string;
  personality: {
    theme: 'feudal' | 'business' | 'family';
    tier: number; // 0=神, 1=王, 2=将, 3=兵
  };
}

// Project State Types
interface ProjectextData {
  id: string;
  name: string;
  type: 'web' | 'mobile' | 'ai' | 'game' | 'desktop';
  status: 'planning' | 'active' | 'completed' | 'archived';
  requirements: string;
  artifacts: string[];
  assignedAgents: string[];
  createdAt: Date;
  updatedAt: Date;
}

// UI Configuration Types
interface UIConfig {
  theme: 'light' | 'dark' | 'auto';
  sidebarWidth: number;
  terminalHeight: number;
  vrmViewerHeight: number;
  showDebugPanel: boolean;
  notificationSettings: {
    sound: boolean;
    volume: number;
    types: string[];
  };
}

// Main Store State
interface MetaStudioState {
  // AI Models Management
  aiModels: AIModelConfig[];
  defaultModel: string;
  
  // Agents Management
  agents: AgentState[];
  currentAgentId: string | null;
  agentHistory: { [agentId: string]: any[] };
  
  // Projects Management
  projexts: ProjectextData[];
  currentProjectextId: string | null;
  
  // UI State
  ui: UIConfig;
  
  // Session State
  isInitialized: boolean;
  lastSyncTime: Date | null;
  
  // Actions
  setCurrentAgent: (agentId: string) => void;
  setAgentModel: (agentId: string, modelId: string) => void;
  updateAgentVRM: (agentId: string, vrmSettings: Partial<AgentState['vrmSettings']>) => void;
  addToAgentHistory: (agentId: string, message: any) => void;
  clearAgentHistory: (agentId: string) => void;
  
  createProjectext: (data: Omit<ProjectextData, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProjectext: (id: string, data: Partial<ProjectextData>) => void;
  setCurrentProjectext: (id: string) => void;
  
  updateUIConfig: (config: Partial<UIConfig>) => void;
  
  // Persistence Actions
  syncState: () => Promise<void>;
  resetStore: () => void;
}

// Default AI Models Configuration
const DEFAULT_AI_MODELS: AIModelConfig[] = [
  {
    id: 'claude-3-haiku',
    name: 'claude-3-haiku',
    displayName: 'Claude 3 Haiku (高速)',
    provider: 'anthropic',
    tier: 'fast',
    contextWindow: 200000,
    pricePerToken: { input: 0.00025, output: 0.00125 },
    capabilities: ['text', 'analysis', 'coding'],
    maxResponseTime: 5,
    availability: 'stable'
  },
  {
    id: 'claude-3-sonnet',
    name: 'claude-3-sonnet', 
    displayName: 'Claude 3 Sonnet (バランス)',
    provider: 'anthropic',
    tier: 'balanced',
    contextWindow: 200000,
    pricePerToken: { input: 0.003, output: 0.015 },
    capabilities: ['text', 'analysis', 'coding', 'creative'],
    maxResponseTime: 10,
    availability: 'stable'
  },
  {
    id: 'claude-3-opus',
    name: 'claude-3-opus',
    displayName: 'Claude 3 Opus (高品質)',
    provider: 'anthropic', 
    tier: 'premium',
    contextWindow: 200000,
    pricePerToken: { input: 0.015, output: 0.075 },
    capabilities: ['text', 'analysis', 'coding', 'creative', 'complex-reasoning'],
    maxResponseTime: 30,
    availability: 'stable'
  },
  {
    id: 'gpt-4',
    name: 'gpt-4',
    displayName: 'GPT-4',
    provider: 'openai',
    tier: 'premium',
    contextWindow: 128000,
    pricePerToken: { input: 0.03, output: 0.06 },
    capabilities: ['text', 'analysis', 'coding'],
    maxResponseTime: 20,
    availability: 'stable'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'gpt-3.5-turbo',
    displayName: 'GPT-3.5 Turbo',
    provider: 'openai',
    tier: 'fast',
    contextWindow: 16385,
    pricePerToken: { input: 0.0005, output: 0.0015 },
    capabilities: ['text', 'analysis'],
    maxResponseTime: 8,
    availability: 'stable'
  }
];

// Default Agents Configuration
const DEFAULT_AGENTS: AgentState[] = [
  {
    id: 'mother-cto',
    name: '母',
    role: 'CTO',
    selectedModel: 'claude-3-sonnet',
    isActive: true,
    vrmSettings: {
      emotion: 'neutral',
      visibility: true
    },
    sessionHistory: [],
    personality: { theme: 'family', tier: 1 }
  },
  {
    id: 'god-ceo',
    name: '神',
    role: 'CEO', 
    selectedModel: 'claude-3-opus',
    isActive: false,
    vrmSettings: {
      emotion: 'neutral',
      visibility: true
    },
    sessionHistory: [],
    personality: { theme: 'feudal', tier: 0 }
  },
  {
    id: 'king-coo',
    name: '王',
    role: 'COO',
    selectedModel: 'claude-3-sonnet',
    isActive: false,
    vrmSettings: {
      emotion: 'neutral',
      visibility: true
    },
    sessionHistory: [],
    personality: { theme: 'feudal', tier: 1 }
  },
  {
    id: 'general-manager',
    name: '将',
    role: 'Manager',
    selectedModel: 'claude-3-haiku',
    isActive: false,
    vrmSettings: {
      emotion: 'neutral',
      visibility: true
    },
    sessionHistory: [],
    personality: { theme: 'feudal', tier: 2 }
  },
  {
    id: 'soldier-worker',
    name: '兵',
    role: 'Worker',
    selectedModel: 'claude-3-haiku',
    isActive: false,
    vrmSettings: {
      emotion: 'neutral', 
      visibility: true
    },
    sessionHistory: [],
    personality: { theme: 'feudal', tier: 3 }
  }
];

// Default UI Configuration
const DEFAULT_UI_CONFIG: UIConfig = {
  theme: 'dark',
  sidebarWidth: 280,
  terminalHeight: 300,
  vrmViewerHeight: 400,
  showDebugPanel: false,
  notificationSettings: {
    sound: true,
    volume: 0.7,
    types: ['completion', 'error', 'agent-switch']
  }
};

// Create Store with Persistence
export const useMetaStudioStore = create<MetaStudioState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        aiModels: DEFAULT_AI_MODELS,
        defaultModel: 'claude-3-sonnet',
        agents: DEFAULT_AGENTS,
        currentAgentId: 'mother-cto',
        agentHistory: {},
        projexts: [],
        currentProjectextId: null,
        ui: DEFAULT_UI_CONFIG,
        isInitialized: false,
        lastSyncTime: null,

        // Agent Actions
        setCurrentAgent: (agentId: string) => 
          set((state) => ({
            currentAgentId: agentId,
            agents: state.agents.map(agent => ({
              ...agent,
              isActive: agent.id === agentId
            }))
          }), false, 'setCurrentAgent'),

        setAgentModel: (agentId: string, modelId: string) =>
          set((state) => ({
            agents: state.agents.map(agent =>
              agent.id === agentId 
                ? { ...agent, selectedModel: modelId }
                : agent
            )
          }), false, 'setAgentModel'),

        updateAgentVRM: (agentId: string, vrmSettings: Partial<AgentState['vrmSettings']>) =>
          set((state) => ({
            agents: state.agents.map(agent =>
              agent.id === agentId
                ? { 
                    ...agent, 
                    vrmSettings: { ...agent.vrmSettings, ...vrmSettings }
                  }
                : agent
            )
          }), false, 'updateAgentVRM'),

        addToAgentHistory: (agentId: string, message: any) =>
          set((state) => ({
            agentHistory: {
              ...state.agentHistory,
              [agentId]: [...(state.agentHistory[agentId] || []), message].slice(-50) // 最新50件保持
            }
          }), false, 'addToAgentHistory'),

        clearAgentHistory: (agentId: string) =>
          set((state) => ({
            agentHistory: {
              ...state.agentHistory,
              [agentId]: []
            }
          }), false, 'clearAgentHistory'),

        // Project Actions
        createProjectext: (data: Omit<ProjectextData, 'id' | 'createdAt' | 'updatedAt'>) =>
          set((state) => {
            const newProjectext: ProjectextData = {
              ...data,
              id: `projext-${Date.now()}`,
              createdAt: new Date(),
              updatedAt: new Date()
            };
            return {
              projexts: [...state.projexts, newProjectext],
              currentProjectextId: newProjectext.id
            };
          }, false, 'createProjectext'),

        updateProjectext: (id: string, data: Partial<ProjectextData>) =>
          set((state) => ({
            projexts: state.projexts.map(projext =>
              projext.id === id
                ? { ...projext, ...data, updatedAt: new Date() }
                : projext
            )
          }), false, 'updateProjectext'),

        setCurrentProjectext: (id: string) =>
          set({ currentProjectextId: id }, false, 'setCurrentProjectext'),

        // UI Actions
        updateUIConfig: (config: Partial<UIConfig>) =>
          set((state) => ({
            ui: { ...state.ui, ...config }
          }), false, 'updateUIConfig'),

        // Persistence Actions
        syncState: async () => {
          // tRPC経由でサーバー同期（将来実装）
          set({ lastSyncTime: new Date() }, false, 'syncState');
        },

        resetStore: () =>
          set({
            agents: DEFAULT_AGENTS,
            currentAgentId: 'mother-cto',
            agentHistory: {},
            projexts: [],
            currentProjectextId: null,
            ui: DEFAULT_UI_CONFIG,
            isInitialized: false,
            lastSyncTime: null
          }, false, 'resetStore')
      }),
      {
        name: 'meta-studio-store',
        storage: createJSONStorage(() => {
          // Smart storage: localStorage for UI, sessionStorage for temporary data
          return {
            getItem: (name: string) => {
              const localStorage_data = localStorage.getItem(name);
              return localStorage_data;
            },
            setItem: (name: string, value: string) => {
              localStorage.setItem(name, value);
            },
            removeItem: (name: string) => {
              localStorage.removeItem(name);
            }
          };
        }),
        partialize: (state) => ({
          // Persist only essential data
          agents: state.agents.map(agent => ({
            ...agent,
            sessionHistory: [] // Don't persist session history
          })),
          currentAgentId: state.currentAgentId,
          ui: state.ui,
          defaultModel: state.defaultModel,
          projexts: state.projexts,
          currentProjectextId: state.currentProjectextId
        }),
        version: 1,
        migrate: (persistedState: any, version: number) => {
          // Handle migration between versions
          if (version === 0) {
            // Migration from legacy localStorage structure
            return {
              ...persistedState,
              isInitialized: false
            };
          }
          return persistedState;
        }
      }
    ),
    {
      name: 'MetaStudioStore',
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);

// Utility Hooks
export const useCurrentAgent = () => {
  const currentAgentId = useMetaStudioStore(state => state.currentAgentId);
  const agents = useMetaStudioStore(state => state.agents);
  return agents.find(agent => agent.id === currentAgentId) || agents[0];
};

export const useAgentModel = (agentId?: string) => {
  const currentAgent = useCurrentAgent();
  const targetAgentId = agentId || currentAgent?.id;
  const agents = useMetaStudioStore(state => state.agents);
  const aiModels = useMetaStudioStore(state => state.aiModels);
  
  const agent = agents.find(a => a.id === targetAgentId);
  const model = aiModels.find(m => m.id === agent?.selectedModel);
  
  return { agent, model };
};

export const useProjectManagement = () => {
  const projexts = useMetaStudioStore(state => state.projexts);
  const currentProjectextId = useMetaStudioStore(state => state.currentProjectextId);
  const createProjectext = useMetaStudioStore(state => state.createProjectext);
  const updateProjectext = useMetaStudioStore(state => state.updateProjectext);
  const setCurrentProjectext = useMetaStudioStore(state => state.setCurrentProjectext);
  
  const currentProjectext = projexts.find(p => p.id === currentProjectextId);
  
  return {
    projexts,
    currentProjectext,
    createProjectext,
    updateProjectext,
    setCurrentProjectext
  };
};

// Store initialization
export const initializeStore = () => {
  const store = useMetaStudioStore.getState();
  if (!store.isInitialized) {
    // Migrate from legacy localStorage if exists
    migrateLegacyStorage();
    useMetaStudioStore.setState({ isInitialized: true });
  }
};

// Legacy data migration
const migrateLegacyStorage = () => {
  try {
    // Migrate from old CharacterContext localStorage
    const oldAgentModels = localStorage.getItem('meta-studio-agent-models');
    const oldAgentVrm = localStorage.getItem('meta-studio-agent-vrm-settings');
    
    if (oldAgentModels) {
      const modelData = JSON.parse(oldAgentModels);
      const { setAgentModel } = useMetaStudioStore.getState();
      
      Object.entries(modelData).forEach(([agentId, modelId]) => {
        setAgentModel(agentId, modelId as string);
      });
      
      console.log('✅ Migrated legacy agent models to Zustand store');
    }
    
    if (oldAgentVrm) {
      const vrmData = JSON.parse(oldAgentVrm);
      const { updateAgentVRM } = useMetaStudioStore.getState();
      
      Object.entries(vrmData).forEach(([agentId, settings]) => {
        updateAgentVRM(agentId, settings as any);
      });
      
      console.log('✅ Migrated legacy VRM settings to Zustand store');
    }
  } catch (error) {
    console.warn('⚠️ Legacy storage migration failed:', error);
  }
};