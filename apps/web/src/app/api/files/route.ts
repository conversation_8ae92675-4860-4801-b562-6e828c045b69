import { NextRequest, NextResponse } from 'next/server';
import { readdir, stat, writeFile, unlink, mkdir, rmdir, rename, rm } from 'fs/promises';
import { join, dirname, extname, basename } from 'path';

export interface FileItem {
  name: string;
  type: 'file' | 'directory';
  path: string;
  fullPath?: string;
  relativePath?: string;
  size?: number;
  modified?: string;
  created?: string;
  extension?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dirPath = searchParams.get('path') || '/';
    const rootPath = searchParams.get('root') || '/Users/<USER>/Dev/meta-studio';
    const getStats = searchParams.get('stats') === 'true';
    
    // セキュリティ: 指定されたプロジェクトディレクトリ内のみアクセス許可
    const projectRoot = rootPath;
    const resolvedPath = join(projectRoot, dirPath.replace(projectRoot, ''));
    
    // プロジェクトルート外へのアクセスを防ぐ
    if (!resolvedPath.startsWith(projectRoot)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // プロジェクト統計情報を取得する場合
    if (getStats) {
      try {
        const stats = await stat(resolvedPath);
        return NextResponse.json({
          path: resolvedPath,
          createdAt: stats.birthtime.toISOString(),
          updatedAt: stats.mtime.toISOString(),
          size: stats.size,
          isDirectory: stats.isDirectory()
        });
      } catch (error) {
        console.error('Stats error:', error);
        // ファイルが存在しない場合は404を返す
        return NextResponse.json({ error: 'File not found' }, { status: 404 });
      }
    }

    const items = await readdir(resolvedPath);
    const fileItems: FileItem[] = [];

    for (const item of items) {
      // 隠しファイル・フォルダをスキップ
      if (item.startsWith('.')) continue;
      
      // node_modules などの除外
      if (['node_modules', 'dist', 'build', '.git', '.next'].includes(item)) continue;

      const fullPath = join(resolvedPath, item);
      const relativePath = fullPath.replace(projectRoot, '').replace(/\\/g, '/');
      
      try {
        const stats = await stat(fullPath);
        
        const fileItem: FileItem = {
          name: item,
          type: stats.isDirectory() ? 'directory' : 'file',
          path: relativePath,
          fullPath: fullPath,
          relativePath: relativePath,
          size: stats.isFile() ? stats.size : undefined,
          modified: stats.mtime.toISOString(),
          created: stats.birthtime.toISOString(),
        };

        if (stats.isFile()) {
          const ext = item.split('.').pop();
          if (ext && ext !== item) {
            fileItem.extension = ext.toLowerCase();
          }
        }

        fileItems.push(fileItem);
      } catch (error) {
        console.error(`Error reading ${item}:`, error);
        continue;
      }
    }

    // ディレクトリを先に、ファイルを後に、名前順でソート
    fileItems.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    return NextResponse.json({
      path: resolvedPath.replace(projectRoot, '') || '/',
      items: fileItems
    });

  } catch (error) {
    console.error('File listing error:', error);
    return NextResponse.json(
      { error: 'Failed to read directory' },
      { status: 500 }
    );
  }
}

// ファイル/フォルダ作成
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, path, name, content = '', type } = body;

    const projectRoot = '/Users/<USER>/Dev/meta-studio';
    const resolvedBasePath = join(projectRoot, path.replace(projectRoot, ''));
    
    // プロジェクトルート外へのアクセスを防ぐ
    if (!resolvedBasePath.startsWith(projectRoot)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    switch (action) {
      case 'create_file': {
        const filePath = join(resolvedBasePath, name);
        await writeFile(filePath, content, 'utf8');
        return NextResponse.json({ success: true, path: filePath });
      }
      
      case 'create_folder': {
        const folderPath = join(resolvedBasePath, name);
        await mkdir(folderPath, { recursive: true });
        return NextResponse.json({ success: true, path: folderPath });
      }
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('File creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create file/folder' },
      { status: 500 }
    );
  }
}

// ファイル/フォルダ削除・リネーム・移動
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filePath = searchParams.get('path');

    console.log('DELETE API called with path:', filePath);

    if (!filePath) {
      return NextResponse.json({ error: 'Path is required' }, { status: 400 });
    }

    const projectRoot = '/Users/<USER>/Dev/meta-studio';
    let resolvedPath: string;

    // パス解決の修正
    if (filePath.startsWith(projectRoot)) {
      // 既に絶対パスの場合
      resolvedPath = filePath;
    } else if (filePath.startsWith('/')) {
      // ルートから始まる場合は、プロジェクトルート内のパスとして扱う
      resolvedPath = join(projectRoot, filePath.substring(1));
    } else {
      // 相対パスの場合
      resolvedPath = join(projectRoot, filePath);
    }

    console.log('Resolved path:', resolvedPath);

    // プロジェクトルート外へのアクセスを防ぐ
    if (!resolvedPath.startsWith(projectRoot)) {
      console.log('Access denied:', resolvedPath, 'not under', projectRoot);
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // ファイル/ディレクトリの存在確認
    try {
      const stats = await stat(resolvedPath);
      console.log('File exists, isDirectory:', stats.isDirectory());

      if (stats.isDirectory()) {
        await rm(resolvedPath, { recursive: true, force: true });
        console.log('Directory deleted successfully');
      } else {
        await unlink(resolvedPath);
        console.log('File deleted successfully');
      }
    } catch (statError) {
      console.error('File not found or permission error:', statError);
      return NextResponse.json({ 
        error: `File not found or permission denied: ${resolvedPath}` 
      }, { status: 404 });
    }

    return NextResponse.json({ success: true, message: 'File/folder deleted successfully' });
  } catch (error) {
    console.error('Delete error:', error);
    return NextResponse.json(
      { error: `Failed to delete file/folder: ${error}` },
      { status: 500 }
    );
  }
}

// ファイル/フォルダのリネーム・移動
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, oldPath, newPath, newName } = body;

    const projectRoot = '/Users/<USER>/Dev/meta-studio';

    switch (action) {
      case 'rename': {
        const resolvedOldPath = join(projectRoot, oldPath.replace(projectRoot, ''));
        const resolvedNewPath = join(projectRoot, newPath.replace(projectRoot, ''));

        // プロジェクトルート外へのアクセスを防ぐ
        if (!resolvedOldPath.startsWith(projectRoot) || !resolvedNewPath.startsWith(projectRoot)) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }

        await rename(resolvedOldPath, resolvedNewPath);
        return NextResponse.json({ success: true, newPath: resolvedNewPath });
      }

      case 'move': {
        const resolvedOldPath = join(projectRoot, oldPath.replace(projectRoot, ''));
        const resolvedNewPath = join(projectRoot, newPath.replace(projectRoot, ''));

        // プロジェクトルート外へのアクセスを防ぐ
        if (!resolvedOldPath.startsWith(projectRoot) || !resolvedNewPath.startsWith(projectRoot)) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }

        // 移動先ディレクトリが存在しない場合は作成
        const targetDir = dirname(resolvedNewPath);
        await mkdir(targetDir, { recursive: true });

        await rename(resolvedOldPath, resolvedNewPath);
        return NextResponse.json({ success: true, newPath: resolvedNewPath });
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('File operation error:', error);
    return NextResponse.json(
      { error: 'Failed to perform file operation' },
      { status: 500 }
    );
  }
}

// ファイル/フォルダリネーム
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { oldPath, newName } = body;
    
    if (!oldPath || !newName) {
      return NextResponse.json({ error: 'Old path and new name are required' }, { status: 400 });
    }

    const projectRoot = '/Users/<USER>/Dev/meta-studio';
    const resolvedOldPath = join(projectRoot, oldPath.replace(projectRoot, ''));
    const resolvedNewPath = join(dirname(resolvedOldPath), newName);
    
    // プロジェクトルート外へのアクセスを防ぐ
    if (!resolvedOldPath.startsWith(projectRoot) || !resolvedNewPath.startsWith(projectRoot)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    await rename(resolvedOldPath, resolvedNewPath);
    
    return NextResponse.json({ success: true, newPath: resolvedNewPath });
  } catch (error) {
    console.error('File rename error:', error);
    return NextResponse.json(
      { error: 'Failed to rename file/folder' },
      { status: 500 }
    );
  }
}