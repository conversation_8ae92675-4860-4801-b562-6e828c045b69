@import "tailwindcss";
@plugin "daisyui";

/* xterm.js CSS - 静的インポート（白背景バグ修正） */
@import "xterm/css/xterm.css";

/* Custom theme: metastudio (DaisyUI v5 optimized) */
[data-theme="metastudio"] {
  /* Primary: Black buttons */
  --p: 0 0 0;
  --pc: 100 100 100;
  
  /* Secondary: Purple accent */
  --s: 256 91 70;
  --sc: 100 100 100;
  
  /* Accent: <PERSON>an highlight */
  --a: 197 100 42;
  --ac: 100 100 100;
  
  /* Neutral: Dark gray */
  --n: 220 31 21;
  --nc: 100 100 100;
  
  /* Base: Dark background theme */
  --b1: 0 0 10;   /* main background */
  --b2: 0 0 0;    /* sidebar background */
  --b3: 0 0 0;    /* terminal background */
  --bc: 96 93 93; /* text color */
  
  /* State colors */
  --in: 198 93 60;
  --su: 158 64 52;
  --wa: 43 96 56;
  --er: 0 91 71;
}

/* Essential styles (minimal overrides) */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
  overflow-x: hidden;
}


/* Neo Meta Neumorphism effects (DaisyUI v5 compatible) */
@layer utilities {
  .neo-glass {
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .neo-depth {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
  }

  .neo-depth:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  .neo-hover {
    transition: all 0.2s ease;
  }

  .neo-hover:hover {
    transform: scale(1.02) translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.5); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Resize active state - global styles for smooth resizing */
body.resize-active {
  cursor: row-resize !important;
  user-select: none !important;
}

body.resize-active * {
  pointer-events: none !important;
}

/* Smooth layout transitions */
.layout-transition {
  transition: height 0.1s ease-out, min-height 0.1s ease-out, max-height 0.1s ease-out;
}

/* Minimal component enhancements (preserve DaisyUI v5 functionality) */
@layer components {
  .btn {
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  }

  .btn:hover:not(:disabled) {
    transform: scale(1.05) translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.12);
  }

  .card {
    transition: all 0.2s ease-in-out;
  }

  .card:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Terminal resize handle enhancements */
  .resize-handle {
    transition: all 0.15s ease-in-out;
    position: relative;
    overflow: hidden;
  }

  .resize-handle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.15s ease;
  }

  .resize-handle:hover::before {
    opacity: 1;
  }

  .resize-handle:active {
    background-color: rgba(59, 130, 246, 0.3) !important;
  }
}
