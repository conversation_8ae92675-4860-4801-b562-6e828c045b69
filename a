
全身と拡大の横にある更新マークが機能していない気がする。
モデルを読み込んでもリロードしたらまた読み込み前に戻るのはなぜ？状態管理や永続化はメタスタジオ要件定義の技術を使えている？
システムプロンプト　神の行動指針と性格を定義するプロンプトです。ここにプロンプト入れても反応しない場合がある。母以外はできない。なぜ？
プレースホルダー系の実装はやめてね。
t-poseの初期状態が嫌だ。もっと自然な状態に出来にない？あの二つのリポジトリ参考にできる？
stagewiseのデフォルト位置は右下じゃなくて左下に移動。
ファイルエクスプローラーで名前の変更とファイルの削除ができない。ファイルのD&Dによる移動もできない
VRMデバッグの虫のマークが反応しない。
対談スタジオが無効化されてる
VRMのTポーズから脱却したい。またリロードしても状態を保存したい。
ペルソナ分離（チャットビューとターミナルでの人格はそれぞれ別。ターミナルは現状ペルソナを設定しても標準で固定されている。選択は実装されているけど反映されていない。）
VRMモデルの永続化(チャットビューでのモデル選択と、対談スタジオでのモデル選択どちらも。)
VTUBERスタジオのフェイストラッキングは機能しているが、モデルを読み込んだらそれと同期されている？試したい。リポジトリはわかってる？VTUBE STUDIOね。さんこうにし。参考にして
aituberとvtuberのコラボのテストする実際のフローを教えて。実装されてる？モデル選択は？UIUXはこれでベスト？特に事前にモデルをセットしておいて顔などがみれて、それを選択するようなUIがいいと思う。今は横長のボタンで微妙。
チャットでエージェントの回答を音声でしゃべるようにしたい。どうすればいい？そう言うボタンは実装されて動くようになっている？
VRMのモーションをするにはどうすれば？あと初期状態のtposeを自然に治したい。参考にしたリポジトリではどう実装してるの？
たとえば将軍エージェントのところで王様のような口調でと伝えて、コアプロンプトを設定しているのにかかわらず、ヘイと送信したらはいなんでしょうかと返信が来る。これはおそらく他のエージェントとうまく機能していないことを表していると思うから、調査して母のエージェントと女王様みたいな口調でと書いたら、それをしっかり従う。ちょっと実装確認してみて欲しい。
afplay /System/Library/Sounds/Ping.aiff & osascript -e 'display notification "🔍 検索・分析完了" with title "Claude Code"'                                    │
Meta Studio調査完了通知 こういう通知完了にyesと答えなくてもいいようにしてほしい。webを検索して実現して。
アプリドックの中のアプリを押したらアプリが起動するようにして、その下のトンカチマークを押したらプロジェクストファイルを開くようにして欲しい。


haconiwaを参考にしてほしい